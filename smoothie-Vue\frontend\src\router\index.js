import { createRouter, createWebHistory } from 'vue-router'
import store from '../store'

// Import page components
import Home from '../pages/Home.vue'
import Menu from '../pages/Menu.vue'
import About from '../pages/About.vue'
import Contact from '../pages/Contact.vue'
import Cart from '../pages/Cart.vue'
import Checkout from '../pages/Checkout.vue'
import Login from '../pages/Login.vue'
import Register from '../pages/Register.vue'
import Profile from '../pages/Profile.vue'
import Orders from '../pages/Orders.vue'
import OrderDetail from '../pages/OrderDetail.vue'
import NotFound from '../pages/NotFound.vue'

// Admin components
import AdminLogin from '../pages/admin/AdminLogin.vue'
import AdminDashboard from '../pages/admin/AdminDashboard.vue'
import AdminMenu from '../pages/admin/AdminMenu.vue'
import AdminOrders from '../pages/admin/AdminOrders.vue'
import AdminUsers from '../pages/admin/AdminUsers.vue'
import AdminSettings from '../pages/admin/AdminSettings.vue'

const routes = [
  // Public routes
  {
    path: '/',
    name: 'Home',
    component: Home,
    meta: {
      title: 'Home - Fresh & Delicious Smoothies',
      description: 'Welcome to Smoothie Vue - Order fresh, healthy smoothies online'
    }
  },
  {
    path: '/menu',
    name: 'Menu',
    component: Menu,
    meta: {
      title: 'Menu - Smoothie Vue',
      description: 'Browse our delicious menu of smoothies, bowls, and juices'
    }
  },
  {
    path: '/menu/:category',
    name: 'MenuCategory',
    component: Menu,
    props: true,
    meta: {
      title: 'Menu Category - Smoothie Vue',
      description: 'Browse smoothies by category'
    }
  },
  {
    path: '/about',
    name: 'About',
    component: About,
    meta: {
      title: 'About Us - Smoothie Vue',
      description: 'Learn about our mission to provide fresh, healthy smoothies'
    }
  },
  {
    path: '/contact',
    name: 'Contact',
    component: Contact,
    meta: {
      title: 'Contact Us - Smoothie Vue',
      description: 'Get in touch with us for questions or feedback'
    }
  },
  
  // Authentication routes
  {
    path: '/login',
    name: 'Login',
    component: Login,
    meta: {
      title: 'Login - Smoothie Vue',
      description: 'Sign in to your account',
      requiresGuest: true
    }
  },
  {
    path: '/register',
    name: 'Register',
    component: Register,
    meta: {
      title: 'Register - Smoothie Vue',
      description: 'Create a new account',
      requiresGuest: true
    }
  },
  
  // Protected user routes
  {
    path: '/cart',
    name: 'Cart',
    component: Cart,
    meta: {
      title: 'Shopping Cart - Smoothie Vue',
      description: 'Review your cart and proceed to checkout',
      requiresAuth: true
    }
  },
  {
    path: '/checkout',
    name: 'Checkout',
    component: Checkout,
    meta: {
      title: 'Checkout - Smoothie Vue',
      description: 'Complete your order',
      requiresAuth: true
    }
  },
  {
    path: '/profile',
    name: 'Profile',
    component: Profile,
    meta: {
      title: 'My Profile - Smoothie Vue',
      description: 'Manage your account settings',
      requiresAuth: true
    }
  },
  {
    path: '/orders',
    name: 'Orders',
    component: Orders,
    meta: {
      title: 'My Orders - Smoothie Vue',
      description: 'View your order history',
      requiresAuth: true
    }
  },
  {
    path: '/orders/:id',
    name: 'OrderDetail',
    component: OrderDetail,
    props: true,
    meta: {
      title: 'Order Details - Smoothie Vue',
      description: 'View order details',
      requiresAuth: true
    }
  },
  
  // Admin routes
  {
    path: '/admin/login',
    name: 'AdminLogin',
    component: AdminLogin,
    meta: {
      title: 'Admin Login - Smoothie Vue',
      description: 'Admin access only',
      requiresGuest: true
    }
  },
  {
    path: '/admin',
    redirect: '/admin/dashboard'
  },
  {
    path: '/admin/dashboard',
    name: 'AdminDashboard',
    component: AdminDashboard,
    meta: {
      title: 'Admin Dashboard - Smoothie Vue',
      description: 'Admin dashboard',
      requiresAdmin: true
    }
  },
  {
    path: '/admin/menu',
    name: 'AdminMenu',
    component: AdminMenu,
    meta: {
      title: 'Menu Management - Smoothie Vue Admin',
      description: 'Manage menu items',
      requiresAdmin: true
    }
  },
  {
    path: '/admin/orders',
    name: 'AdminOrders',
    component: AdminOrders,
    meta: {
      title: 'Order Management - Smoothie Vue Admin',
      description: 'Manage customer orders',
      requiresAdmin: true
    }
  },
  {
    path: '/admin/users',
    name: 'AdminUsers',
    component: AdminUsers,
    meta: {
      title: 'User Management - Smoothie Vue Admin',
      description: 'Manage user accounts',
      requiresAdmin: true
    }
  },
  {
    path: '/admin/settings',
    name: 'AdminSettings',
    component: AdminSettings,
    meta: {
      title: 'Settings - Smoothie Vue Admin',
      description: 'Admin settings',
      requiresAdmin: true
    }
  },
  
  // 404 route
  {
    path: '/:pathMatch(.*)*',
    name: 'NotFound',
    component: NotFound,
    meta: {
      title: 'Page Not Found - Smoothie Vue',
      description: 'The page you are looking for does not exist'
    }
  }
]

const router = createRouter({
  history: createWebHistory(process.env.BASE_URL),
  routes,
  scrollBehavior(to, from, savedPosition) {
    if (savedPosition) {
      return savedPosition
    } else if (to.hash) {
      return {
        el: to.hash,
        behavior: 'smooth'
      }
    } else {
      return { top: 0 }
    }
  }
})

// Navigation guards
router.beforeEach((to, from, next) => {
  // Update page title and meta description
  if (to.meta.title) {
    document.title = to.meta.title
  }
  
  if (to.meta.description) {
    const metaDescription = document.querySelector('meta[name="description"]')
    if (metaDescription) {
      metaDescription.setAttribute('content', to.meta.description)
    }
  }
  
  // Check authentication requirements
  const isAuthenticated = store.getters.isAuthenticated
  const isAdmin = store.getters.isAdmin
  
  if (to.meta.requiresAuth && !isAuthenticated) {
    next({
      name: 'Login',
      query: { redirect: to.fullPath }
    })
  } else if (to.meta.requiresAdmin && !isAdmin) {
    next({
      name: 'AdminLogin',
      query: { redirect: to.fullPath }
    })
  } else if (to.meta.requiresGuest && isAuthenticated) {
    next({ name: 'Home' })
  } else {
    next()
  }
})

router.afterEach((to, from) => {
  // Analytics tracking (if implemented)
  if (typeof gtag !== 'undefined') {
    gtag('config', 'GA_MEASUREMENT_ID', {
      page_title: to.meta.title,
      page_location: window.location.href
    })
  }
  
  // Close mobile menu after navigation
  const navbar = document.querySelector('.navbar-collapse')
  if (navbar && navbar.classList.contains('show')) {
    navbar.classList.remove('show')
  }
})

export default router
