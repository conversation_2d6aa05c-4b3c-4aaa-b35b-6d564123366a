<template>
  <div class="home-page">
    <!-- Hero Section -->
    <section class="hero-section">
      <div class="container">
        <div class="row align-items-center min-vh-100">
          <div class="col-lg-6">
            <div class="hero-content">
              <h1 class="hero-title">
                Fresh & Delicious
                <span class="text-primary">Smoothies</span>
              </h1>
              <p class="hero-description">
                Made with premium ingredients and packed with nutrients. 
                Start your healthy journey with our amazing smoothie collection.
              </p>
              <div class="hero-actions">
                <router-link to="/menu" class="btn btn-primary btn-lg me-3">
                  <i class="fas fa-utensils me-2"></i>
                  Browse Menu
                </router-link>
                <router-link to="/about" class="btn btn-outline-primary btn-lg">
                  <i class="fas fa-info-circle me-2"></i>
                  Learn More
                </router-link>
              </div>
            </div>
          </div>
          <div class="col-lg-6">
            <div class="hero-image">
              <div class="image-placeholder">
                <i class="fas fa-leaf text-primary"></i>
                <p>Hero Image</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Features Section -->
    <section class="features-section py-5">
      <div class="container">
        <div class="row text-center mb-5">
          <div class="col-12">
            <h2 class="section-title">Why Choose Smoothie Vue?</h2>
            <p class="section-subtitle">We're committed to providing the best smoothie experience</p>
          </div>
        </div>
        <div class="row">
          <div class="col-md-4 mb-4">
            <div class="feature-card">
              <div class="feature-icon">
                <i class="fas fa-leaf"></i>
              </div>
              <h4>Fresh Ingredients</h4>
              <p>We use only the freshest fruits and vegetables, sourced locally when possible.</p>
            </div>
          </div>
          <div class="col-md-4 mb-4">
            <div class="feature-card">
              <div class="feature-icon">
                <i class="fas fa-heart"></i>
              </div>
              <h4>Healthy & Nutritious</h4>
              <p>Packed with vitamins, minerals, and antioxidants to fuel your active lifestyle.</p>
            </div>
          </div>
          <div class="col-md-4 mb-4">
            <div class="feature-card">
              <div class="feature-icon">
                <i class="fas fa-clock"></i>
              </div>
              <h4>Quick & Convenient</h4>
              <p>Order online and pick up in minutes, or enjoy delivery to your doorstep.</p>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Featured Items Section -->
    <section class="featured-section py-5 bg-light">
      <div class="container">
        <div class="row text-center mb-5">
          <div class="col-12">
            <h2 class="section-title">Featured Smoothies</h2>
            <p class="section-subtitle">Try our most popular and newest creations</p>
          </div>
        </div>
        
        <div v-if="isLoading" class="text-center">
          <LoadingSpinner message="Loading featured items..." />
        </div>
        
        <div v-else class="row">
          <div 
            v-for="item in featuredItems" 
            :key="item.food_id" 
            class="col-lg-3 col-md-6 mb-4"
          >
            <div class="food-card">
              <div class="food-image">
                <div class="image-placeholder">
                  <i class="fas fa-glass-whiskey text-primary"></i>
                </div>
                <div class="food-badge" v-if="item.food_status">
                  {{ item.food_status }}
                </div>
              </div>
              <div class="food-content">
                <h5 class="food-name">{{ item.food_name }}</h5>
                <p class="food-description">{{ item.food_desc }}</p>
                <div class="food-rating">
                  <span class="stars">
                    <i v-for="n in 5" :key="n" 
                       :class="n <= item.food_star ? 'fas fa-star' : 'far fa-star'">
                    </i>
                  </span>
                  <span class="rating-text">({{ item.food_vote }})</span>
                </div>
                <div class="food-price">
                  <span v-if="item.food_discount > 0" class="original-price">
                    ${{ item.food_price.toFixed(2) }}
                  </span>
                  <span class="current-price">
                    ${{ (item.food_price - item.food_discount).toFixed(2) }}
                  </span>
                </div>
                <button class="btn btn-primary btn-sm w-100 mt-2" @click="addToCart(item)">
                  <i class="fas fa-plus me-1"></i>
                  Add to Cart
                </button>
              </div>
            </div>
          </div>
        </div>
        
        <div class="text-center mt-4">
          <router-link to="/menu" class="btn btn-outline-primary">
            View Full Menu
            <i class="fas fa-arrow-right ms-2"></i>
          </router-link>
        </div>
      </div>
    </section>

    <!-- CTA Section -->
    <section class="cta-section py-5">
      <div class="container">
        <div class="row text-center">
          <div class="col-12">
            <h2 class="text-white mb-3">Ready to Start Your Healthy Journey?</h2>
            <p class="text-white-50 mb-4">Join thousands of satisfied customers who have made the switch to healthier living.</p>
            <router-link to="/register" class="btn btn-light btn-lg">
              <i class="fas fa-user-plus me-2"></i>
              Get Started Today
            </router-link>
          </div>
        </div>
      </div>
    </section>
  </div>
</template>

<script>
import { mapGetters, mapActions } from 'vuex'

export default {
  name: 'Home',
  
  computed: {
    ...mapGetters('menu', ['featuredFoods', 'isLoading']),
    
    featuredItems() {
      return this.featuredFoods.slice(0, 4) // Show only 4 items
    }
  },
  
  methods: {
    ...mapActions('menu', ['loadFeaturedItems']),
    ...mapActions('cart', ['addToCart']),
    
    async addToCart(food) {
      try {
        await this.addToCart({ food, quantity: 1 })
        this.showSuccess(`${food.food_name} added to cart!`)
      } catch (error) {
        this.handleApiError(error, 'Failed to add item to cart')
      }
    }
  },
  
  async mounted() {
    try {
      await this.loadFeaturedItems()
    } catch (error) {
      console.error('Error loading featured items:', error)
    }
  }
}
</script>

<style scoped>
.hero-section {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  min-height: 100vh;
  display: flex;
  align-items: center;
}

.hero-title {
  font-size: 3.5rem;
  font-weight: 700;
  line-height: 1.2;
  margin-bottom: 1.5rem;
}

.hero-description {
  font-size: 1.25rem;
  color: var(--text-secondary);
  margin-bottom: 2rem;
  line-height: 1.6;
}

.hero-actions {
  margin-top: 2rem;
}

.hero-image {
  text-align: center;
}

.image-placeholder {
  width: 400px;
  height: 400px;
  background: white;
  border-radius: 20px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  box-shadow: 0 10px 30px rgba(0,0,0,0.1);
  margin: 0 auto;
}

.image-placeholder i {
  font-size: 4rem;
  margin-bottom: 1rem;
}

.section-title {
  font-size: 2.5rem;
  font-weight: 600;
  color: var(--text-dark);
  margin-bottom: 1rem;
}

.section-subtitle {
  font-size: 1.1rem;
  color: var(--text-secondary);
  max-width: 600px;
  margin: 0 auto;
}

.feature-card {
  text-align: center;
  padding: 2rem 1rem;
  height: 100%;
}

.feature-icon {
  width: 80px;
  height: 80px;
  background: var(--primary-color);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 1.5rem;
}

.feature-icon i {
  font-size: 2rem;
  color: white;
}

.feature-card h4 {
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: var(--text-dark);
}

.feature-card p {
  color: var(--text-secondary);
  line-height: 1.6;
}

.food-card {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 6px rgba(0,0,0,0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  height: 100%;
}

.food-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.food-image {
  position: relative;
  height: 200px;
  background: #f8f9fa;
  display: flex;
  align-items: center;
  justify-content: center;
}

.food-image .image-placeholder {
  width: 100%;
  height: 100%;
  margin: 0;
  border-radius: 0;
  box-shadow: none;
}

.food-badge {
  position: absolute;
  top: 10px;
  right: 10px;
  background: var(--primary-color);
  color: white;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 500;
  text-transform: capitalize;
}

.food-content {
  padding: 1.5rem;
}

.food-name {
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: var(--text-dark);
}

.food-description {
  color: var(--text-secondary);
  font-size: 0.9rem;
  margin-bottom: 1rem;
  line-height: 1.4;
}

.food-rating {
  display: flex;
  align-items: center;
  margin-bottom: 1rem;
}

.stars {
  color: #ffc107;
  margin-right: 0.5rem;
}

.rating-text {
  font-size: 0.85rem;
  color: var(--text-secondary);
}

.food-price {
  margin-bottom: 1rem;
}

.original-price {
  text-decoration: line-through;
  color: var(--text-muted);
  margin-right: 0.5rem;
}

.current-price {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--primary-color);
}

.cta-section {
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
}

/* Responsive Design */
@media (max-width: 768px) {
  .hero-title {
    font-size: 2.5rem;
  }
  
  .hero-description {
    font-size: 1.1rem;
  }
  
  .image-placeholder {
    width: 300px;
    height: 300px;
  }
  
  .section-title {
    font-size: 2rem;
  }
}
</style>
