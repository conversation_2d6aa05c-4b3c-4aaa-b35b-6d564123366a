// Cart Controller
import {
    getAllItems,
    getAItem,
    insertToCart,
    updateCartItemQty,
    deleteItemInCart,
    deleteAllItemsByUser,
    getCartSummary
} from "../models/CartModel.js";

// Get all cart items for a user
export const allItems = (req, res) => {
    const userId = req.params.id;
    getAllItems(userId, (err, results) => {
        if (err) {
            res.status(500).json({ 
                message: "Error fetching cart items", 
                error: err.message 
            });
        } else {
            res.json({
                success: true,
                data: results,
                count: results.length
            });
        }
    });
};

// Get specific cart item
export const getItem = (req, res) => {
    const userId = req.params.user_id;
    const foodId = req.params.food_id;
    
    getAItem(userId, foodId, (err, results) => {
        if (err) {
            res.status(500).json({ 
                message: "Error fetching cart item", 
                error: err.message 
            });
        } else {
            res.json({
                success: true,
                data: results.length > 0 ? results[0] : null
            });
        }
    });
};

// Add item to cart
export const addItems = (req, res) => {
    const data = req.body;
    
    // Basic validation
    if (!data.user_id || !data.food_id) {
        return res.status(400).json({ 
            message: "Missing required fields: user_id, food_id" 
        });
    }

    // Set default quantity if not provided
    if (!data.item_qty) {
        data.item_qty = 1;
    }

    insertToCart(data, (err, result) => {
        if (err) {
            res.status(500).json({ 
                message: "Error adding item to cart", 
                error: err.message 
            });
        } else {
            res.status(201).json({
                success: true,
                message: "Item added to cart successfully",
                data: result
            });
        }
    });
};

// Update cart item quantity
export const updateItem = (req, res) => {
    const data = req.body;
    
    // Basic validation
    if (!data.user_id || !data.food_id || !data.item_qty) {
        return res.status(400).json({ 
            message: "Missing required fields: user_id, food_id, item_qty" 
        });
    }

    // Validate quantity
    if (data.item_qty < 1) {
        return res.status(400).json({ 
            message: "Quantity must be at least 1" 
        });
    }

    updateCartItemQty(data, (err, result) => {
        if (err) {
            res.status(500).json({ 
                message: "Error updating cart item", 
                error: err.message 
            });
        } else if (result.affectedRows === 0) {
            res.status(404).json({ 
                message: "Cart item not found" 
            });
        } else {
            res.json({
                success: true,
                message: "Cart item updated successfully"
            });
        }
    });
};

// Delete specific item from cart
export const deleteItem = (req, res) => {
    const userId = req.params.user_id;
    const foodId = req.params.food_id;

    deleteItemInCart(userId, foodId, (err, result) => {
        if (err) {
            res.status(500).json({ 
                message: "Error deleting cart item", 
                error: err.message 
            });
        } else if (result.affectedRows === 0) {
            res.status(404).json({ 
                message: "Cart item not found" 
            });
        } else {
            res.json({
                success: true,
                message: "Item removed from cart successfully"
            });
        }
    });
};

// Clear all items from user's cart
export const deleteItems = (req, res) => {
    const userId = req.params.id;

    deleteAllItemsByUser(userId, (err, result) => {
        if (err) {
            res.status(500).json({ 
                message: "Error clearing cart", 
                error: err.message 
            });
        } else {
            res.json({
                success: true,
                message: "Cart cleared successfully",
                deletedCount: result.affectedRows
            });
        }
    });
};

// Get cart summary
export const getCartSummaryData = (req, res) => {
    const userId = req.params.id;

    getCartSummary(userId, (err, result) => {
        if (err) {
            res.status(500).json({ 
                message: "Error fetching cart summary", 
                error: err.message 
            });
        } else {
            res.json({
                success: true,
                data: {
                    total_items: result.total_items || 0,
                    total_quantity: result.total_quantity || 0,
                    total_price: parseFloat(result.total_price || 0).toFixed(2)
                }
            });
        }
    });
};
