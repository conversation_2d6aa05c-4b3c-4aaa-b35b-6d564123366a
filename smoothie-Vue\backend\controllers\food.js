// Food Controller
import {
    getFoods,
    getFoodById,
    getFoodsByCategory,
    insertFood,
    updateFood,
    deleteFood,
    searchFoods
} from "../models/FoodModel.js";

// Get all foods
export const showFoods = (req, res) => {
    getFoods((err, results) => {
        if (err) {
            res.status(500).json({ 
                message: "Error fetching foods", 
                error: err.message 
            });
        } else {
            res.json({
                success: true,
                data: results,
                count: results.length
            });
        }
    });
};

// Get single food by ID
export const showFoodById = (req, res) => {
    const id = req.params.id;
    getFoodById(id, (err, result) => {
        if (err) {
            res.status(500).json({ 
                message: "Error fetching food", 
                error: err.message 
            });
        } else if (!result) {
            res.status(404).json({ 
                message: "Food item not found" 
            });
        } else {
            res.json({
                success: true,
                data: result
            });
        }
    });
};

// Get foods by category
export const showFoodsByCategory = (req, res) => {
    const category = req.params.category;
    getFoodsByCategory(category, (err, results) => {
        if (err) {
            res.status(500).json({ 
                message: "Error fetching foods by category", 
                error: err.message 
            });
        } else {
            res.json({
                success: true,
                data: results,
                count: results.length,
                category: category
            });
        }
    });
};

// Create new food
export const createFood = (req, res) => {
    const data = req.body;
    
    // Basic validation
    if (!data.food_name || !data.food_price || !data.food_category) {
        return res.status(400).json({ 
            message: "Missing required fields: food_name, food_price, food_category" 
        });
    }

    insertFood(data, (err, result) => {
        if (err) {
            res.status(500).json({ 
                message: "Error creating food", 
                error: err.message 
            });
        } else {
            res.status(201).json({
                success: true,
                message: "Food created successfully",
                data: { id: result.insertId, ...data }
            });
        }
    });
};

// Update food
export const updateFoodItem = (req, res) => {
    const id = req.params.id;
    const data = req.body;

    updateFood(id, data, (err, result) => {
        if (err) {
            res.status(500).json({ 
                message: "Error updating food", 
                error: err.message 
            });
        } else if (result.affectedRows === 0) {
            res.status(404).json({ 
                message: "Food item not found" 
            });
        } else {
            res.json({
                success: true,
                message: "Food updated successfully"
            });
        }
    });
};

// Delete food
export const deleteFoodItem = (req, res) => {
    const id = req.params.id;

    deleteFood(id, (err, result) => {
        if (err) {
            res.status(500).json({ 
                message: "Error deleting food", 
                error: err.message 
            });
        } else if (result.affectedRows === 0) {
            res.status(404).json({ 
                message: "Food item not found" 
            });
        } else {
            res.json({
                success: true,
                message: "Food deleted successfully"
            });
        }
    });
};

// Search foods
export const searchFoodItems = (req, res) => {
    const searchTerm = req.query.q;
    
    if (!searchTerm) {
        return res.status(400).json({ 
            message: "Search term is required" 
        });
    }

    searchFoods(searchTerm, (err, results) => {
        if (err) {
            res.status(500).json({ 
                message: "Error searching foods", 
                error: err.message 
            });
        } else {
            res.json({
                success: true,
                data: results,
                count: results.length,
                searchTerm: searchTerm
            });
        }
    });
};
