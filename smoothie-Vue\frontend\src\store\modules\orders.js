// Orders Store Module
const state = {
  orders: [],
  currentOrder: null,
  isLoading: false,
  error: null
}

const getters = {
  allOrders: state => state.orders,
  currentOrder: state => state.currentOrder,
  orderById: state => id => state.orders.find(order => order.order_id === id),
  recentOrders: state => state.orders.slice(0, 5),
  ordersByStatus: state => status => state.orders.filter(order => order.order_status === status),
  isLoading: state => state.isLoading
}

const mutations = {
  SET_ORDERS(state, orders) {
    state.orders = orders
  },
  
  SET_CURRENT_ORDER(state, order) {
    state.currentOrder = order
  },
  
  ADD_ORDER(state, order) {
    state.orders.unshift(order) // Add to beginning
  },
  
  UPDATE_ORDER(state, updatedOrder) {
    const index = state.orders.findIndex(order => order.order_id === updatedOrder.order_id)
    if (index !== -1) {
      state.orders.splice(index, 1, updatedOrder)
    }
  },
  
  SET_LOADING(state, loading) {
    state.isLoading = loading
  },
  
  SET_ERROR(state, error) {
    state.error = error
  }
}

const actions = {
  // Load user orders
  async loadOrders({ commit, rootGetters }) {
    try {
      commit('SET_LOADING', true)
      commit('SET_ERROR', null)
      
      const userId = rootGetters['auth/userId']
      if (!userId) {
        commit('SET_ORDERS', [])
        return
      }
      
      // Mock orders data - replace with API call
      const mockOrders = [
        {
          order_id: 1,
          user_id: userId,
          order_total: 25.97,
          order_status: 'completed',
          payment_method: 'credit_card',
          payment_status: 'paid',
          delivery_type: 'pickup',
          created_at: '2024-01-15T10:30:00Z',
          items: [
            {
              food_id: 1,
              food_name: 'Tropical Paradise',
              quantity: 2,
              unit_price: 8.99
            },
            {
              food_id: 2,
              food_name: 'Berry Blast',
              quantity: 1,
              unit_price: 7.99
            }
          ]
        },
        {
          order_id: 2,
          user_id: userId,
          order_total: 12.99,
          order_status: 'preparing',
          payment_method: 'credit_card',
          payment_status: 'paid',
          delivery_type: 'pickup',
          estimated_time: 15,
          created_at: '2024-01-16T14:20:00Z',
          items: [
            {
              food_id: 4,
              food_name: 'Acai Power Bowl',
              quantity: 1,
              unit_price: 12.99
            }
          ]
        }
      ]
      
      commit('SET_ORDERS', mockOrders)
      
    } catch (error) {
      commit('SET_ERROR', error.message)
      console.error('Error loading orders:', error)
    } finally {
      commit('SET_LOADING', false)
    }
  },
  
  // Create new order
  async createOrder({ commit, rootGetters }, orderData) {
    try {
      commit('SET_LOADING', true)
      commit('SET_ERROR', null)
      
      const userId = rootGetters['auth/userId']
      if (!userId) {
        throw new Error('User must be logged in to place an order')
      }
      
      // This would be an API call in a real app
      const newOrder = {
        order_id: Date.now(),
        user_id: userId,
        order_total: orderData.total,
        order_status: 'pending',
        payment_method: orderData.paymentMethod,
        payment_status: 'pending',
        delivery_type: orderData.deliveryType || 'pickup',
        delivery_address: orderData.deliveryAddress,
        special_instructions: orderData.specialInstructions,
        estimated_time: 20, // minutes
        created_at: new Date().toISOString(),
        items: orderData.items
      }
      
      commit('ADD_ORDER', newOrder)
      commit('SET_CURRENT_ORDER', newOrder)
      
      return { success: true, order: newOrder }
      
    } catch (error) {
      commit('SET_ERROR', error.message)
      throw error
    } finally {
      commit('SET_LOADING', false)
    }
  },
  
  // Update order status (admin)
  async updateOrderStatus({ commit }, { orderId, status }) {
    try {
      commit('SET_LOADING', true)
      
      // This would be an API call in a real app
      const updatedOrder = {
        ...state.orders.find(order => order.order_id === orderId),
        order_status: status,
        updated_at: new Date().toISOString()
      }
      
      commit('UPDATE_ORDER', updatedOrder)
      
      return { success: true, order: updatedOrder }
      
    } catch (error) {
      commit('SET_ERROR', error.message)
      throw error
    } finally {
      commit('SET_LOADING', false)
    }
  },
  
  // Get order details
  async getOrderDetails({ commit, state }, orderId) {
    try {
      commit('SET_LOADING', true)
      
      // Check if order is already in state
      let order = state.orders.find(o => o.order_id === orderId)
      
      if (!order) {
        // This would be an API call in a real app
        // For now, return null if not found
        order = null
      }
      
      commit('SET_CURRENT_ORDER', order)
      
      return order
      
    } catch (error) {
      commit('SET_ERROR', error.message)
      throw error
    } finally {
      commit('SET_LOADING', false)
    }
  },
  
  // Cancel order
  async cancelOrder({ commit }, orderId) {
    try {
      commit('SET_LOADING', true)
      
      // This would be an API call in a real app
      const updatedOrder = {
        ...state.orders.find(order => order.order_id === orderId),
        order_status: 'cancelled',
        updated_at: new Date().toISOString()
      }
      
      commit('UPDATE_ORDER', updatedOrder)
      
      return { success: true, order: updatedOrder }
      
    } catch (error) {
      commit('SET_ERROR', error.message)
      throw error
    } finally {
      commit('SET_LOADING', false)
    }
  },
  
  // Reorder (add items to cart)
  async reorder({ dispatch }, order) {
    try {
      // Add each item from the order to the cart
      for (const item of order.items) {
        await dispatch('cart/addToCart', {
          food: {
            food_id: item.food_id,
            food_name: item.food_name,
            food_price: item.unit_price,
            food_discount: 0
          },
          quantity: item.quantity
        }, { root: true })
      }
      
      return { success: true }
      
    } catch (error) {
      console.error('Error reordering:', error)
      throw error
    }
  }
}

export default {
  namespaced: true,
  state,
  getters,
  mutations,
  actions
}
