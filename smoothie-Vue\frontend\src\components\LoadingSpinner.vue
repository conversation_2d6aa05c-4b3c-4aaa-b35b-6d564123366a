<template>
  <div class="loading-spinner-container" :class="{ 'inline': inline }">
    <div class="loading-spinner" :class="size"></div>
    <p v-if="message && !inline" class="loading-message">{{ message }}</p>
  </div>
</template>

<script>
export default {
  name: 'LoadingSpinner',
  
  props: {
    size: {
      type: String,
      default: 'medium',
      validator: value => ['small', 'medium', 'large'].includes(value)
    },
    message: {
      type: String,
      default: ''
    },
    inline: {
      type: Boolean,
      default: false
    }
  }
}
</script>

<style scoped>
.loading-spinner-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
}

.loading-spinner-container.inline {
  display: inline-flex;
  padding: 0;
}

.loading-spinner {
  border-radius: 50%;
  border-style: solid;
  border-color: var(--gray-200, #e9ecef);
  border-top-color: var(--primary-color, #27ae60);
  animation: spin 1s linear infinite;
}

.loading-spinner.small {
  width: 20px;
  height: 20px;
  border-width: 2px;
}

.loading-spinner.medium {
  width: 40px;
  height: 40px;
  border-width: 4px;
}

.loading-spinner.large {
  width: 60px;
  height: 60px;
  border-width: 6px;
}

.loading-message {
  margin-top: 1rem;
  color: var(--text-secondary, #7f8c8d);
  font-weight: 500;
  text-align: center;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
</style>
