<template>
  <div class="notification-system">
    <transition-group name="notification" tag="div" class="notification-container">
      <div
        v-for="notification in notifications"
        :key="notification.id"
        :class="['notification', `notification-${notification.type}`]"
        @click="removeNotification(notification.id)"
      >
        <div class="notification-content">
          <i :class="getNotificationIcon(notification.type)" class="notification-icon"></i>
          <div class="notification-text">
            <div v-if="notification.title" class="notification-title">
              {{ notification.title }}
            </div>
            <div class="notification-message">
              {{ notification.message }}
            </div>
          </div>
          <button class="notification-close" @click.stop="removeNotification(notification.id)">
            <i class="fas fa-times"></i>
          </button>
        </div>
        <div 
          v-if="notification.duration > 0"
          class="notification-progress"
          :style="{ animationDuration: notification.duration + 'ms' }"
        ></div>
      </div>
    </transition-group>
  </div>
</template>

<script>
import { mapState, mapActions } from 'vuex'

export default {
  name: 'NotificationSystem',
  
  computed: {
    ...mapState('notifications', ['notifications'])
  },
  
  methods: {
    ...mapActions('notifications', ['removeNotification']),
    
    getNotificationIcon(type) {
      const icons = {
        success: 'fas fa-check-circle',
        error: 'fas fa-exclamation-circle',
        warning: 'fas fa-exclamation-triangle',
        info: 'fas fa-info-circle'
      }
      return icons[type] || icons.info
    }
  }
}
</script>

<style scoped>
.notification-system {
  position: fixed;
  top: 100px;
  right: 20px;
  z-index: 9999;
  pointer-events: none;
}

.notification-container {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.notification {
  min-width: 300px;
  max-width: 400px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  overflow: hidden;
  pointer-events: auto;
  cursor: pointer;
  position: relative;
}

.notification-content {
  display: flex;
  align-items: flex-start;
  padding: 16px;
  gap: 12px;
}

.notification-icon {
  font-size: 20px;
  margin-top: 2px;
  flex-shrink: 0;
}

.notification-text {
  flex: 1;
}

.notification-title {
  font-weight: 600;
  margin-bottom: 4px;
  font-size: 14px;
}

.notification-message {
  font-size: 14px;
  line-height: 1.4;
  color: #666;
}

.notification-close {
  background: none;
  border: none;
  color: #999;
  cursor: pointer;
  padding: 0;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.2s ease;
  flex-shrink: 0;
}

.notification-close:hover {
  background-color: rgba(0, 0, 0, 0.1);
  color: #666;
}

.notification-progress {
  height: 3px;
  background: currentColor;
  opacity: 0.3;
  animation: progress linear forwards;
}

/* Notification Types */
.notification-success {
  border-left: 4px solid #2ecc71;
}

.notification-success .notification-icon {
  color: #2ecc71;
}

.notification-error {
  border-left: 4px solid #e74c3c;
}

.notification-error .notification-icon {
  color: #e74c3c;
}

.notification-warning {
  border-left: 4px solid #f1c40f;
}

.notification-warning .notification-icon {
  color: #f1c40f;
}

.notification-info {
  border-left: 4px solid #3498db;
}

.notification-info .notification-icon {
  color: #3498db;
}

/* Animations */
.notification-enter-active {
  transition: all 0.3s ease;
}

.notification-leave-active {
  transition: all 0.3s ease;
}

.notification-enter-from {
  opacity: 0;
  transform: translateX(100%);
}

.notification-leave-to {
  opacity: 0;
  transform: translateX(100%);
}

@keyframes progress {
  from {
    width: 100%;
  }
  to {
    width: 0%;
  }
}

/* Mobile responsive */
@media (max-width: 768px) {
  .notification-system {
    top: 80px;
    right: 10px;
    left: 10px;
  }
  
  .notification {
    min-width: auto;
    max-width: none;
  }
}
</style>
