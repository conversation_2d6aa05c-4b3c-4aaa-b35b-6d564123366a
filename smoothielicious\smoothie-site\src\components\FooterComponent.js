import React from 'react';
import { Con<PERSON><PERSON>, <PERSON>, <PERSON>, But<PERSON>, Form, FormGroup, Input } from 'reactstrap';
import { Link } from 'react-router-dom';

const FooterComponent = () => {
  return (
    <footer className="bg-dark text-white py-5">
      <Container>
        <Row>
          <Col md={4} className="mb-4 mb-md-0">
            <h5 className="text-uppercase mb-4">Smoothielicious</h5>
            <p>
              We're passionate about creating delicious and nutritious smoothies
              using only the freshest ingredients. Our mission is to help you
              live a healthier lifestyle one smoothie at a time.
            </p>
            <div className="social-icons">
              <a href="https://facebook.com" target="_blank" rel="noopener noreferrer" className="text-white mr-3">
                <i className="fab fa-facebook-f fa-lg"></i>
              </a>
              <a href="https://instagram.com" target="_blank" rel="noopener noreferrer" className="text-white mr-3">
                <i className="fab fa-instagram fa-lg"></i>
              </a>
              <a href="https://twitter.com" target="_blank" rel="noopener noreferrer" className="text-white mr-3">
                <i className="fab fa-twitter fa-lg"></i>
              </a>
            </div>
          </Col>
          
          <Col md={2} className="mb-4 mb-md-0">
            <h5 className="text-uppercase mb-4">Quick Links</h5>
            <ul className="list-unstyled">
              <li className="mb-2">
                <Link to="/" className="text-white">Home</Link>
              </li>
              <li className="mb-2">
                <Link to="/menu" className="text-white">Menu</Link>
              </li>
              <li className="mb-2">
                <Link to="/offers" className="text-white">Special Offers</Link>
              </li>
              <li className="mb-2">
                <Link to="/order" className="text-white">Order Online</Link>
              </li>
              <li className="mb-2">
                <Link to="/cart" className="text-white">Cart</Link>
              </li>
            </ul>
          </Col>
          
          <Col md={3} className="mb-4 mb-md-0">
            <h5 className="text-uppercase mb-4">Contact Us</h5>
            <ul className="list-unstyled">
              <li className="mb-2">
                <i className="fas fa-map-marker-alt mr-2"></i> 123 Smoothie Lane, Fruitville, FL 12345
              </li>
              <li className="mb-2">
                <i className="fas fa-phone mr-2"></i> (*************
              </li>
              <li className="mb-2">
                <i className="fas fa-envelope mr-2"></i> <EMAIL>
              </li>
              <li className="mb-2">
                <i className="fas fa-clock mr-2"></i> Mon-Fri: 8am-8pm, Sat-Sun: 9am-7pm
              </li>
            </ul>
          </Col>
          
          <Col md={3}>
            <h5 className="text-uppercase mb-4">Newsletter</h5>
            <p>Subscribe to our newsletter for updates and special offers.</p>
            <Form>
              <FormGroup>
                <Input 
                  type="email" 
                  placeholder="Your email address" 
                  className="mb-2"
                />
                <Button color="success" block>Subscribe</Button>
              </FormGroup>
            </Form>
          </Col>
        </Row>
        
        <hr className="my-4 bg-light" />
        
        <Row>
          <Col className="text-center">
            <p className="mb-0">
              &copy; {new Date().getFullYear()} Smoothielicious. All rights reserved.
            </p>
          </Col>
        </Row>
      </Container>
    </footer>
  );
};

export default FooterComponent;
