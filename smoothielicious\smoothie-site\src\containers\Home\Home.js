import React, { Component } from 'react';
import { Container, Row, Col, Button, Card, CardBody, CardTitle, CardText } from 'reactstrap';
import { Link } from 'react-router-dom';
import Navigation from '../../components/Navigation/Navigation';
import Footer from '../../components/Footer/Footer';

class Home extends Component {
  render() {
    const { data, count } = this.props;
    
    return (
      <div>
        <Navigation cartCount={count} />
        
        {/* Hero Section */}
        <div className="hero">
          <Container>
            <h1>{data.title}</h1>
            <p>{data.subtitle}</p>
            <p>{data.description}</p>
            <Button 
              color="success" 
              size="lg" 
              tag={Link} 
              to="/menu"
              className="mr-3"
            >
              View Our Menu
            </Button>
            <Button 
              color="warning" 
              size="lg" 
              tag={Link} 
              to="/order"
            >
              Order Online
            </Button>
          </Container>
        </div>
        
        {/* Features Section */}
        <Container className="py-5">
          <h2 className="text-center mb-5">Why Choose Smo<PERSON>icious?</h2>
          <Row>
            {data.features && data.features.map(feature => (
              <Col md={4} key={feature.id} className="mb-4">
                <Card className="h-100 text-center">
                  <CardBody>
                    <div className="mb-3">
                      <i className={`fas fa-${feature.icon} fa-3x text-success`}></i>
                    </div>
                    <CardTitle tag="h4">{feature.title}</CardTitle>
                    <CardText>{feature.description}</CardText>
                  </CardBody>
                </Card>
              </Col>
            ))}
          </Row>
        </Container>
        
        {/* Call to Action */}
        <div className="bg-light py-5">
          <Container className="text-center">
            <h2 className="mb-4">Ready to try our delicious smoothies?</h2>
            <p className="lead mb-4">Order online for pickup or delivery!</p>
            <Button 
              color="success" 
              size="lg" 
              tag={Link} 
              to="/order"
            >
              Order Now
            </Button>
          </Container>
        </div>
        
        <Footer />
      </div>
    );
  }
}

export default Home;
