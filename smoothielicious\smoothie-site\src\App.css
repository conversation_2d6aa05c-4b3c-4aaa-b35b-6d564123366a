/* Smoothielicious App Styles */

:root {
  --primary-color: #4CAF50;  /* Green */
  --secondary-color: #FF9800;  /* Orange */
  --accent-color: #E91E63;  /* Pink */
  --light-color: #F5F5F5;
  --dark-color: #333333;
  --text-color: #212121;
  --text-light: #757575;
}

body {
  font-family: 'Roboto', 'Open Sans', sans-serif;
  color: var(--text-color);
  background-color: var(--light-color);
  margin: 0;
  padding: 0;
}

.App {
  text-align: center;
}

/* Header Styles */
.navbar {
  background-color: var(--primary-color) !important;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.navbar-brand {
  color: white !important;
  font-weight: bold;
  font-size: 1.5rem;
}

.nav-link {
  color: white !important;
  font-weight: 500;
  margin: 0 10px;
  transition: all 0.3s ease;
}

.nav-link:hover {
  color: var(--secondary-color) !important;
  transform: translateY(-2px);
}

/* Hero Section */
.hero {
  background-image: linear-gradient(rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0.5)), url('https://images.unsplash.com/photo-1589734580748-6d9421464885?ixlib=rb-1.2.1&auto=format&fit=crop&w=1350&q=80');
  background-size: cover;
  background-position: center;
  color: white;
  padding: 100px 0;
  text-align: center;
}

.hero h1 {
  font-size: 3rem;
  margin-bottom: 20px;
}

.hero p {
  font-size: 1.2rem;
  max-width: 600px;
  margin: 0 auto 30px;
}

/* Menu Section */
.menu-section {
  padding: 60px 0;
}

.menu-item {
  margin-bottom: 30px;
  transition: all 0.3s ease;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.menu-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
}

.menu-item img {
  width: 100%;
  height: 200px;
  object-fit: cover;
}

.menu-item-content {
  padding: 20px;
}

.menu-item h3 {
  color: var(--primary-color);
  margin-bottom: 10px;
}

.menu-item p {
  color: var(--text-light);
  margin-bottom: 15px;
}

.menu-item .price {
  font-weight: bold;
  color: var(--accent-color);
  font-size: 1.2rem;
}

.add-to-cart {
  background-color: var(--primary-color);
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.add-to-cart:hover {
  background-color: var(--secondary-color);
}

/* Cart Section */
.cart-section {
  padding: 60px 0;
}

.cart-item {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  padding: 15px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.cart-item img {
  width: 80px;
  height: 80px;
  object-fit: cover;
  border-radius: 4px;
  margin-right: 15px;
}

.cart-item-details {
  flex-grow: 1;
}

.cart-item h4 {
  margin: 0 0 5px;
  color: var(--primary-color);
}

.cart-item .price {
  color: var(--accent-color);
  font-weight: bold;
}

.cart-item .quantity {
  display: flex;
  align-items: center;
  margin-top: 10px;
}

.quantity-btn {
  background-color: var(--light-color);
  border: 1px solid var(--text-light);
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

.quantity-value {
  margin: 0 10px;
  font-weight: bold;
}

/* Footer */
footer {
  background-color: var(--dark-color);
  color: white;
  padding: 40px 0;
  margin-top: 60px;
}

.footer-links h5 {
  color: var(--secondary-color);
  margin-bottom: 20px;
}

.footer-links ul {
  list-style: none;
  padding: 0;
}

.footer-links li {
  margin-bottom: 10px;
}

.footer-links a {
  color: white;
  text-decoration: none;
  transition: all 0.3s ease;
}

.footer-links a:hover {
  color: var(--secondary-color);
}

.social-icons {
  display: flex;
  margin-top: 20px;
}

.social-icons a {
  color: white;
  margin-right: 15px;
  font-size: 1.5rem;
  transition: all 0.3s ease;
}

.social-icons a:hover {
  color: var(--secondary-color);
  transform: translateY(-3px);
}

/* Nav Pills */
.nav-pills .nav-link.active {
  background-color: var(--primary-color) !important;
}

.nav-pills .nav-link {
  color: var(--text-color) !important;
  margin: 0 5px;
}

.nav-pills .nav-link:hover {
  color: var(--primary-color) !important;
}

/* Buttons */
.btn-success {
  background-color: var(--primary-color) !important;
  border-color: var(--primary-color) !important;
}

.btn-success:hover {
  background-color: #3d8b40 !important;
  border-color: #3d8b40 !important;
}

.btn-warning {
  background-color: var(--secondary-color) !important;
  border-color: var(--secondary-color) !important;
  color: white !important;
}

.btn-warning:hover {
  background-color: #e68a00 !important;
  border-color: #e68a00 !important;
}

/* Badge */
.badge-success {
  background-color: var(--primary-color) !important;
}

/* Responsive */
@media (max-width: 768px) {
  .hero h1 {
    font-size: 2rem;
  }
  
  .hero p {
    font-size: 1rem;
  }
}
