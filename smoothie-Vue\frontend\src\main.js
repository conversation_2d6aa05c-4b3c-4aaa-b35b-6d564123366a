import { createApp } from 'vue'
import App from './App.vue'
import router from './router'
import store from './store'

// Bootstrap CSS and JS
import 'bootstrap/dist/css/bootstrap.min.css'
import 'bootstrap/dist/js/bootstrap.bundle.min.js'

// Font Awesome
import '@fortawesome/fontawesome-free/css/all.min.css'

// Global styles
import './assets/css/global.css'

// API service
import apiService from './services/api'

// Utility functions
import { formatCurrency, formatDate, truncateText } from './utils/helpers'

// Create Vue app
const app = createApp(App)

// Global properties
app.config.globalProperties.$api = apiService
app.config.globalProperties.$formatCurrency = formatCurrency
app.config.globalProperties.$formatDate = formatDate
app.config.globalProperties.$truncateText = truncateText

// Global error handler
app.config.errorHandler = (err, instance, info) => {
  console.error('Global error:', err)
  console.error('Component instance:', instance)
  console.error('Error info:', info)
  
  // Send error to store for user notification
  if (store) {
    store.dispatch('showNotification', {
      type: 'error',
      message: 'An unexpected error occurred. Please try again.',
      duration: 5000
    })
  }
}

// Global warning handler
app.config.warnHandler = (msg, instance, trace) => {
  console.warn('Vue warning:', msg)
  console.warn('Component trace:', trace)
}

// Register global components
import LoadingSpinner from './components/LoadingSpinner.vue'
import ErrorMessage from './components/ErrorMessage.vue'
import ConfirmDialog from './components/ConfirmDialog.vue'

app.component('LoadingSpinner', LoadingSpinner)
app.component('ErrorMessage', ErrorMessage)
app.component('ConfirmDialog', ConfirmDialog)

// Global directives
app.directive('focus', {
  mounted(el) {
    el.focus()
  }
})

app.directive('click-outside', {
  beforeMount(el, binding) {
    el.clickOutsideEvent = function(event) {
      if (!(el === event.target || el.contains(event.target))) {
        binding.value(event, el)
      }
    }
    document.body.addEventListener('click', el.clickOutsideEvent)
  },
  unmounted(el) {
    document.body.removeEventListener('click', el.clickOutsideEvent)
  }
})

// Global mixins
app.mixin({
  methods: {
    // Common method to handle API errors
    handleApiError(error, defaultMessage = 'An error occurred') {
      console.error('API Error:', error)
      
      let message = defaultMessage
      if (error.response && error.response.data && error.response.data.message) {
        message = error.response.data.message
      } else if (error.message) {
        message = error.message
      }
      
      this.$store.dispatch('showNotification', {
        type: 'error',
        message: message,
        duration: 5000
      })
    },
    
    // Common method to show success messages
    showSuccess(message, duration = 3000) {
      this.$store.dispatch('showNotification', {
        type: 'success',
        message: message,
        duration: duration
      })
    },
    
    // Common method to show info messages
    showInfo(message, duration = 3000) {
      this.$store.dispatch('showNotification', {
        type: 'info',
        message: message,
        duration: duration
      })
    },
    
    // Common method to show warning messages
    showWarning(message, duration = 4000) {
      this.$store.dispatch('showNotification', {
        type: 'warning',
        message: message,
        duration: duration
      })
    },
    
    // Scroll to top of page
    scrollToTop() {
      window.scrollTo({
        top: 0,
        behavior: 'smooth'
      })
    },
    
    // Scroll to element
    scrollToElement(elementId) {
      const element = document.getElementById(elementId)
      if (element) {
        element.scrollIntoView({
          behavior: 'smooth',
          block: 'start'
        })
      }
    }
  }
})

// Use plugins
app.use(store)
app.use(router)

// Mount the app
app.mount('#app')

// Service Worker registration (for PWA)
if ('serviceWorker' in navigator && process.env.NODE_ENV === 'production') {
  window.addEventListener('load', () => {
    navigator.serviceWorker.register('/sw.js')
      .then((registration) => {
        console.log('SW registered: ', registration)
      })
      .catch((registrationError) => {
        console.log('SW registration failed: ', registrationError)
      })
  })
}

// Performance monitoring
if (process.env.NODE_ENV === 'development') {
  // Log performance metrics in development
  window.addEventListener('load', () => {
    setTimeout(() => {
      const perfData = performance.getEntriesByType('navigation')[0]
      console.log('Page Load Performance:', {
        'DNS Lookup': perfData.domainLookupEnd - perfData.domainLookupStart,
        'TCP Connection': perfData.connectEnd - perfData.connectStart,
        'Request': perfData.responseStart - perfData.requestStart,
        'Response': perfData.responseEnd - perfData.responseStart,
        'DOM Processing': perfData.domComplete - perfData.domLoading,
        'Total Load Time': perfData.loadEventEnd - perfData.navigationStart
      })
    }, 0)
  })
}
