import { createStore } from 'vuex'
import apiService from '../services/api'

// Import store modules
import auth from './modules/auth'
import menu from './modules/menu'
import cart from './modules/cart'
import orders from './modules/orders'
import notifications from './modules/notifications'

const store = createStore({
  state: {
    // Global loading state
    isLoading: false,
    loadingMessage: '',
    
    // App initialization
    isAppInitialized: false,
    
    // API status
    apiStatus: 'unknown', // 'online', 'offline', 'unknown'
    
    // UI state
    isMobileMenuOpen: false,
    isCartWidgetOpen: false,
    
    // App settings
    settings: {
      currency: 'USD',
      currencySymbol: '$',
      deliveryFee: 5.99,
      freeDeliveryThreshold: 25.00,
      taxRate: 0.08,
      businessHours: {
        monday: { open: '07:00', close: '20:00', closed: false },
        tuesday: { open: '07:00', close: '20:00', closed: false },
        wednesday: { open: '07:00', close: '20:00', closed: false },
        thursday: { open: '07:00', close: '20:00', closed: false },
        friday: { open: '07:00', close: '21:00', closed: false },
        saturday: { open: '08:00', close: '21:00', closed: false },
        sunday: { open: '08:00', close: '19:00', closed: false }
      }
    }
  },
  
  getters: {
    // Loading state
    isLoading: state => state.isLoading,
    loadingMessage: state => state.loadingMessage,
    
    // App state
    isAppInitialized: state => state.isAppInitialized,
    apiStatus: state => state.apiStatus,
    
    // UI state
    isMobileMenuOpen: state => state.isMobileMenuOpen,
    isCartWidgetOpen: state => state.isCartWidgetOpen,
    
    // Settings
    settings: state => state.settings,
    currency: state => state.settings.currency,
    currencySymbol: state => state.settings.currencySymbol,
    deliveryFee: state => state.settings.deliveryFee,
    freeDeliveryThreshold: state => state.settings.freeDeliveryThreshold,
    taxRate: state => state.settings.taxRate,
    
    // Business hours
    businessHours: state => state.settings.businessHours,
    isBusinessOpen: state => {
      const now = new Date()
      const currentDay = now.toLocaleLowerCase().substring(0, 3) + 
                        now.toLocaleLowerCase().substring(3)
      const daySchedule = state.settings.businessHours[currentDay]
      
      if (daySchedule.closed) return false
      
      const currentTime = now.getHours() * 100 + now.getMinutes()
      const openTime = parseInt(daySchedule.open.replace(':', ''))
      const closeTime = parseInt(daySchedule.close.replace(':', ''))
      
      return currentTime >= openTime && currentTime <= closeTime
    }
  },
  
  mutations: {
    // Loading mutations
    SET_LOADING(state, { isLoading, message = '' }) {
      state.isLoading = isLoading
      state.loadingMessage = message
    },
    
    // App initialization
    SET_APP_INITIALIZED(state, initialized) {
      state.isAppInitialized = initialized
    },
    
    // API status
    SET_API_STATUS(state, status) {
      state.apiStatus = status
    },
    
    // UI mutations
    SET_MOBILE_MENU_OPEN(state, isOpen) {
      state.isMobileMenuOpen = isOpen
    },
    
    SET_CART_WIDGET_OPEN(state, isOpen) {
      state.isCartWidgetOpen = isOpen
    },
    
    // Settings mutations
    UPDATE_SETTINGS(state, settings) {
      state.settings = { ...state.settings, ...settings }
    },
    
    UPDATE_BUSINESS_HOURS(state, businessHours) {
      state.settings.businessHours = { ...state.settings.businessHours, ...businessHours }
    }
  },
  
  actions: {
    // Initialize the application
    async initializeApp({ commit, dispatch }) {
      try {
        commit('SET_LOADING', { isLoading: true, message: 'Initializing application...' })
        
        // Check API status
        await dispatch('checkApiStatus')
        
        // Load initial data
        await dispatch('menu/loadCategories')
        await dispatch('menu/loadFeaturedItems')
        
        // Check for stored authentication
        await dispatch('auth/checkStoredAuth')
        
        commit('SET_APP_INITIALIZED', true)
        
      } catch (error) {
        console.error('Failed to initialize app:', error)
        dispatch('notifications/showNotification', {
          type: 'error',
          message: 'Failed to initialize application. Some features may not work properly.'
        })
      } finally {
        commit('SET_LOADING', { isLoading: false })
      }
    },
    
    // Check API status
    async checkApiStatus({ commit }) {
      try {
        await apiService.get('/health')
        commit('SET_API_STATUS', 'online')
      } catch (error) {
        commit('SET_API_STATUS', 'offline')
        console.error('API is offline:', error)
      }
    },
    
    // UI actions
    toggleMobileMenu({ commit, state }) {
      commit('SET_MOBILE_MENU_OPEN', !state.isMobileMenuOpen)
    },
    
    closeMobileMenu({ commit }) {
      commit('SET_MOBILE_MENU_OPEN', false)
    },
    
    toggleCartWidget({ commit, state }) {
      commit('SET_CART_WIDGET_OPEN', !state.isCartWidgetOpen)
    },
    
    closeCartWidget({ commit }) {
      commit('SET_CART_WIDGET_OPEN', false)
    },
    
    // Settings actions
    async loadSettings({ commit }) {
      try {
        // In a real app, this would load from API
        // For now, we'll use default settings
        const settings = {
          currency: 'USD',
          currencySymbol: '$',
          deliveryFee: 5.99,
          freeDeliveryThreshold: 25.00,
          taxRate: 0.08
        }
        
        commit('UPDATE_SETTINGS', settings)
      } catch (error) {
        console.error('Failed to load settings:', error)
      }
    },
    
    async updateSettings({ commit }, settings) {
      try {
        // In a real app, this would save to API
        commit('UPDATE_SETTINGS', settings)
        
        return { success: true }
      } catch (error) {
        console.error('Failed to update settings:', error)
        throw error
      }
    },
    
    // Global error handler
    handleError({ dispatch }, { error, message = 'An error occurred' }) {
      console.error('Global error:', error)
      
      dispatch('notifications/showNotification', {
        type: 'error',
        message: message,
        duration: 5000
      })
    }
  },
  
  modules: {
    auth,
    menu,
    cart,
    orders,
    notifications
  },
  
  // Enable strict mode in development
  strict: process.env.NODE_ENV !== 'production'
})

export default store
