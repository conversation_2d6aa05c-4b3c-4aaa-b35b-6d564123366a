// Cart Store Module
const state = {
  items: [],
  isLoading: false,
  error: null
}

const getters = {
  cartItems: state => state.items,
  cartItemCount: state => state.items.reduce((total, item) => total + item.quantity, 0),
  cartTotal: state => {
    return state.items.reduce((total, item) => {
      const price = item.food_price - (item.food_discount || 0)
      return total + (price * item.quantity)
    }, 0)
  },
  cartSubtotal: (state, getters) => getters.cartTotal,
  cartTax: (state, getters, rootState, rootGetters) => {
    const taxRate = rootGetters.taxRate || 0.08
    return getters.cartTotal * taxRate
  },
  cartGrandTotal: (state, getters) => {
    return getters.cartTotal + getters.cartTax
  },
  isItemInCart: state => foodId => {
    return state.items.some(item => item.food_id === foodId)
  },
  getCartItem: state => foodId => {
    return state.items.find(item => item.food_id === foodId)
  }
}

const mutations = {
  SET_CART_ITEMS(state, items) {
    state.items = items
  },
  
  ADD_TO_CART(state, item) {
    const existingItem = state.items.find(cartItem => cartItem.food_id === item.food_id)
    
    if (existingItem) {
      existingItem.quantity += item.quantity || 1
    } else {
      state.items.push({
        ...item,
        quantity: item.quantity || 1,
        added_at: new Date().toISOString()
      })
    }
  },
  
  UPDATE_CART_ITEM(state, { food_id, quantity }) {
    const item = state.items.find(cartItem => cartItem.food_id === food_id)
    if (item) {
      item.quantity = quantity
    }
  },
  
  REMOVE_FROM_CART(state, food_id) {
    state.items = state.items.filter(item => item.food_id !== food_id)
  },
  
  CLEAR_CART(state) {
    state.items = []
  },
  
  SET_LOADING(state, loading) {
    state.isLoading = loading
  },
  
  SET_ERROR(state, error) {
    state.error = error
  }
}

const actions = {
  // Load cart data
  async loadCartData({ commit, rootGetters }) {
    try {
      commit('SET_LOADING', true)
      commit('SET_ERROR', null)
      
      const userId = rootGetters['auth/userId']
      if (!userId) {
        // Load from localStorage for guest users
        const savedCart = localStorage.getItem('cart_items')
        if (savedCart) {
          const items = JSON.parse(savedCart)
          commit('SET_CART_ITEMS', items)
        }
        return
      }
      
      // This would be an API call in a real app
      // For now, load from localStorage
      const savedCart = localStorage.getItem(`cart_items_${userId}`)
      if (savedCart) {
        const items = JSON.parse(savedCart)
        commit('SET_CART_ITEMS', items)
      }
      
    } catch (error) {
      commit('SET_ERROR', error.message)
      console.error('Error loading cart:', error)
    } finally {
      commit('SET_LOADING', false)
    }
  },
  
  // Add item to cart
  async addToCart({ commit, state, rootGetters }, { food, quantity = 1, customizations = {} }) {
    try {
      commit('SET_LOADING', true)
      commit('SET_ERROR', null)
      
      const cartItem = {
        food_id: food.food_id,
        food_name: food.food_name,
        food_price: food.food_price,
        food_discount: food.food_discount || 0,
        food_src: food.food_src,
        food_desc: food.food_desc,
        quantity: quantity,
        customizations: customizations
      }
      
      commit('ADD_TO_CART', cartItem)
      
      // Save to localStorage
      const userId = rootGetters['auth/userId']
      const storageKey = userId ? `cart_items_${userId}` : 'cart_items'
      localStorage.setItem(storageKey, JSON.stringify(state.items))
      
      return { success: true }
      
    } catch (error) {
      commit('SET_ERROR', error.message)
      throw error
    } finally {
      commit('SET_LOADING', false)
    }
  },
  
  // Update item quantity
  async updateItemQuantity({ commit, state, rootGetters }, { food_id, quantity }) {
    try {
      if (quantity <= 0) {
        return this.dispatch('cart/removeFromCart', food_id)
      }
      
      commit('UPDATE_CART_ITEM', { food_id, quantity })
      
      // Save to localStorage
      const userId = rootGetters['auth/userId']
      const storageKey = userId ? `cart_items_${userId}` : 'cart_items'
      localStorage.setItem(storageKey, JSON.stringify(state.items))
      
      return { success: true }
      
    } catch (error) {
      commit('SET_ERROR', error.message)
      throw error
    }
  },
  
  // Remove item from cart
  async removeFromCart({ commit, state, rootGetters }, food_id) {
    try {
      commit('REMOVE_FROM_CART', food_id)
      
      // Save to localStorage
      const userId = rootGetters['auth/userId']
      const storageKey = userId ? `cart_items_${userId}` : 'cart_items'
      localStorage.setItem(storageKey, JSON.stringify(state.items))
      
      return { success: true }
      
    } catch (error) {
      commit('SET_ERROR', error.message)
      throw error
    }
  },
  
  // Clear entire cart
  async clearCart({ commit, rootGetters }) {
    try {
      commit('CLEAR_CART')
      
      // Clear from localStorage
      const userId = rootGetters['auth/userId']
      const storageKey = userId ? `cart_items_${userId}` : 'cart_items'
      localStorage.removeItem(storageKey)
      
      return { success: true }
      
    } catch (error) {
      commit('SET_ERROR', error.message)
      throw error
    }
  },
  
  // Sync cart after login
  async syncCartAfterLogin({ commit, state, rootGetters }) {
    try {
      const userId = rootGetters['auth/userId']
      if (!userId) return
      
      // Get guest cart
      const guestCart = localStorage.getItem('cart_items')
      
      // Get user cart
      const userCart = localStorage.getItem(`cart_items_${userId}`)
      
      let finalCart = []
      
      if (guestCart && userCart) {
        // Merge carts
        const guestItems = JSON.parse(guestCart)
        const userItems = JSON.parse(userCart)
        
        // Combine items, adding quantities for duplicates
        const mergedItems = [...userItems]
        
        guestItems.forEach(guestItem => {
          const existingItem = mergedItems.find(item => item.food_id === guestItem.food_id)
          if (existingItem) {
            existingItem.quantity += guestItem.quantity
          } else {
            mergedItems.push(guestItem)
          }
        })
        
        finalCart = mergedItems
      } else if (guestCart) {
        finalCart = JSON.parse(guestCart)
      } else if (userCart) {
        finalCart = JSON.parse(userCart)
      }
      
      // Update state and storage
      commit('SET_CART_ITEMS', finalCart)
      localStorage.setItem(`cart_items_${userId}`, JSON.stringify(finalCart))
      localStorage.removeItem('cart_items') // Clear guest cart
      
    } catch (error) {
      console.error('Error syncing cart after login:', error)
    }
  }
}

export default {
  namespaced: true,
  state,
  getters,
  mutations,
  actions
}
