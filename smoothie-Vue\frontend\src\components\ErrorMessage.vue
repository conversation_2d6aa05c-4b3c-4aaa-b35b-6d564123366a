<template>
  <div class="error-message" :class="variant">
    <div class="error-content">
      <i :class="iconClass" class="error-icon"></i>
      <div class="error-text">
        <h6 v-if="title" class="error-title">{{ title }}</h6>
        <p class="error-description">{{ message }}</p>
        <button v-if="showRetry" @click="$emit('retry')" class="btn btn-sm btn-outline-danger">
          <i class="fas fa-redo me-1"></i>Try Again
        </button>
      </div>
      <button v-if="dismissible" @click="$emit('dismiss')" class="btn-close" aria-label="Close"></button>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ErrorMessage',
  
  props: {
    title: {
      type: String,
      default: ''
    },
    message: {
      type: String,
      required: true
    },
    variant: {
      type: String,
      default: 'danger',
      validator: value => ['danger', 'warning', 'info'].includes(value)
    },
    showRetry: {
      type: Boolean,
      default: false
    },
    dismissible: {
      type: Boolean,
      default: false
    }
  },
  
  emits: ['retry', 'dismiss'],
  
  computed: {
    iconClass() {
      const icons = {
        danger: 'fas fa-exclamation-circle',
        warning: 'fas fa-exclamation-triangle',
        info: 'fas fa-info-circle'
      }
      return icons[this.variant] || icons.danger
    }
  }
}
</script>

<style scoped>
.error-message {
  border-radius: 8px;
  padding: 1rem;
  margin: 1rem 0;
  border: 1px solid;
}

.error-message.danger {
  background-color: #f8d7da;
  border-color: #f5c6cb;
  color: #721c24;
}

.error-message.warning {
  background-color: #fff3cd;
  border-color: #ffeaa7;
  color: #856404;
}

.error-message.info {
  background-color: #d1ecf1;
  border-color: #bee5eb;
  color: #0c5460;
}

.error-content {
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
}

.error-icon {
  font-size: 1.25rem;
  margin-top: 0.125rem;
  flex-shrink: 0;
}

.error-text {
  flex: 1;
}

.error-title {
  margin: 0 0 0.5rem 0;
  font-weight: 600;
  font-size: 1rem;
}

.error-description {
  margin: 0 0 0.75rem 0;
  line-height: 1.5;
}

.btn-close {
  background: none;
  border: none;
  font-size: 1.25rem;
  cursor: pointer;
  opacity: 0.7;
  flex-shrink: 0;
}

.btn-close:hover {
  opacity: 1;
}

.btn {
  padding: 0.375rem 0.75rem;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  font-weight: 500;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  cursor: pointer;
  transition: all 0.15s ease-in-out;
}

.btn-outline-danger {
  color: #dc3545;
  border: 1px solid #dc3545;
  background: transparent;
}

.btn-outline-danger:hover {
  background-color: #dc3545;
  color: white;
}
</style>
