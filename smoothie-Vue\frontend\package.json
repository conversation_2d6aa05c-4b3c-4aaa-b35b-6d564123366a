{"name": "smoothie-vue-frontend", "version": "1.0.0", "description": "Smoothie Vue Restaurant Ordering System Frontend", "private": true, "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build", "lint": "vue-cli-service lint", "dev": "vue-cli-service serve --open"}, "dependencies": {"axios": "^1.6.0", "core-js": "^3.8.3", "vue": "^3.3.0", "vue-router": "^4.2.0", "vuex": "^4.1.0", "bootstrap": "^5.3.0", "bootstrap-vue-next": "^0.15.0", "@fortawesome/fontawesome-free": "^6.4.0"}, "devDependencies": {"@babel/core": "^7.12.16", "@babel/eslint-parser": "^7.12.16", "@vue/cli-plugin-babel": "~5.0.0", "@vue/cli-plugin-eslint": "~5.0.0", "@vue/cli-service": "~5.0.0", "eslint": "^7.32.0", "eslint-plugin-vue": "^8.0.3"}, "eslintConfig": {"root": true, "env": {"node": true}, "extends": ["plugin:vue/vue3-essential", "eslint:recommended"], "parserOptions": {"parser": "@babel/eslint-parser", "requireConfigFile": false}, "rules": {"vue/multi-word-component-names": "off", "no-unused-vars": "warn"}}, "browserslist": ["> 1%", "last 2 versions", "not dead", "not ie 11"]}