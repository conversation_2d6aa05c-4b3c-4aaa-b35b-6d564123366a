// User Controller
import {
    getAllUsers,
    getUserByEmail,
    getUserById,
    insertUser,
    updateUser,
    deleteUser,
    checkEmailExists
} from "../models/UserModel.js";

// Get all users (admin only)
export const showAllUsers = (req, res) => {
    getAllUsers((err, results) => {
        if (err) {
            res.status(500).json({ 
                message: "Error fetching users", 
                error: err.message 
            });
        } else {
            res.json({
                success: true,
                data: results,
                count: results.length
            });
        }
    });
};

// Get user by email (for login)
export const showAUser = (req, res) => {
    const email = req.params.email;
    getUserByEmail(email, (err, result) => {
        if (err) {
            res.status(500).json({ 
                message: "Error fetching user", 
                error: err.message 
            });
        } else if (!result) {
            res.status(404).json({ 
                message: "User not found" 
            });
        } else {
            // Remove password from response for security
            const { user_password, ...userWithoutPassword } = result;
            res.json({
                success: true,
                data: userWithoutPassword
            });
        }
    });
};

// Get user by ID
export const showUserById = (req, res) => {
    const id = req.params.id;
    getUserById(id, (err, result) => {
        if (err) {
            res.status(500).json({ 
                message: "Error fetching user", 
                error: err.message 
            });
        } else if (!result) {
            res.status(404).json({ 
                message: "User not found" 
            });
        } else {
            res.json({
                success: true,
                data: result
            });
        }
    });
};

// Create new user account
export const createAccount = (req, res) => {
    const data = req.body;
    
    // Basic validation
    if (!data.user_name || !data.user_email || !data.user_password) {
        return res.status(400).json({ 
            message: "Missing required fields: user_name, user_email, user_password" 
        });
    }

    // Email format validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(data.user_email)) {
        return res.status(400).json({ 
            message: "Invalid email format" 
        });
    }

    // Check if email already exists
    checkEmailExists(data.user_email, (err, exists) => {
        if (err) {
            return res.status(500).json({ 
                message: "Error checking email", 
                error: err.message 
            });
        }
        
        if (exists) {
            return res.status(409).json({ 
                message: "Email already exists" 
            });
        }

        // Create user
        insertUser(data, (insertErr, result) => {
            if (insertErr) {
                res.status(500).json({ 
                    message: "Error creating account", 
                    error: insertErr.message 
                });
            } else {
                res.status(201).json({
                    success: true,
                    message: "Account created successfully",
                    data: { 
                        user_id: result.insertId, 
                        user_name: data.user_name,
                        user_email: data.user_email
                    }
                });
            }
        });
    });
};

// Update user
export const updateUserAccount = (req, res) => {
    const id = req.params.id;
    const data = req.body;

    // Remove password from update data for security (handle separately)
    const { user_password, ...updateData } = data;

    updateUser(id, updateData, (err, result) => {
        if (err) {
            res.status(500).json({ 
                message: "Error updating user", 
                error: err.message 
            });
        } else if (result.affectedRows === 0) {
            res.status(404).json({ 
                message: "User not found" 
            });
        } else {
            res.json({
                success: true,
                message: "User updated successfully"
            });
        }
    });
};

// Delete user
export const deleteUserAccount = (req, res) => {
    const id = req.params.id;

    deleteUser(id, (err, result) => {
        if (err) {
            res.status(500).json({ 
                message: "Error deleting user", 
                error: err.message 
            });
        } else if (result.affectedRows === 0) {
            res.status(404).json({ 
                message: "User not found" 
            });
        } else {
            res.json({
                success: true,
                message: "User deleted successfully"
            });
        }
    });
};

// Login user (basic implementation)
export const loginUser = (req, res) => {
    const { user_email, user_password } = req.body;
    
    if (!user_email || !user_password) {
        return res.status(400).json({ 
            message: "Email and password are required" 
        });
    }

    getUserByEmail(user_email, (err, user) => {
        if (err) {
            res.status(500).json({ 
                message: "Error during login", 
                error: err.message 
            });
        } else if (!user) {
            res.status(401).json({ 
                message: "Invalid credentials" 
            });
        } else if (user.user_password !== user_password) {
            // Note: In production, use proper password hashing
            res.status(401).json({ 
                message: "Invalid credentials" 
            });
        } else {
            // Remove password from response
            const { user_password: _, ...userWithoutPassword } = user;
            res.json({
                success: true,
                message: "Login successful",
                data: userWithoutPassword
            });
        }
    });
};
