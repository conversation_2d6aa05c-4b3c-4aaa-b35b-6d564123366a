<template>
  <div id="app">
    <!-- Admin Layout -->
    <div v-if="isAdminRoute" class="admin-layout">
      <router-view />
    </div>
    
    <!-- Main Layout -->
    <div v-else class="main-layout">
      <!-- Navigation -->
      <NavBar />
      
      <!-- Main Content -->
      <main class="main-content">
        <router-view />
      </main>
      
      <!-- Footer -->
      <FooterComponent />
      
      <!-- Global Cart Widget -->
      <CartWidget v-if="showCartWidget" />
      
      <!-- Notification System -->
      <NotificationSystem />
    </div>
    
    <!-- Loading Overlay -->
    <LoadingOverlay v-if="isLoading" />
  </div>
</template>

<script>
import { mapState, mapActions, mapGetters } from 'vuex'
import NavBar from './components/NavBar.vue'
import FooterComponent from './components/FooterComponent.vue'
import CartWidget from './components/CartWidget.vue'
import NotificationSystem from './components/NotificationSystem.vue'
import LoadingOverlay from './components/LoadingOverlay.vue'

export default {
  name: 'App',
  
  components: {
    NavBar,
    FooterComponent,
    CartWidget,
    NotificationSystem,
    LoadingOverlay
  },
  
  computed: {
    ...mapState(['isLoading']),
    ...mapGetters(['isAuthenticated', 'cartItemCount']),
    
    isAdminRoute() {
      return this.$route.path.startsWith('/admin')
    },
    
    showCartWidget() {
      return this.isAuthenticated && this.cartItemCount > 0 && !this.isAdminRoute
    }
  },
  
  methods: {
    ...mapActions(['initializeApp', 'loadUserData', 'loadCartData']),
    
    handleRouteChange() {
      // Scroll to top on route change
      window.scrollTo(0, 0)
      
      // Close mobile menu if open
      this.closeMobileMenu()
    },
    
    closeMobileMenu() {
      const navbar = document.querySelector('.navbar-collapse')
      if (navbar && navbar.classList.contains('show')) {
        navbar.classList.remove('show')
      }
    },
    
    handleResize() {
      // Handle responsive behavior
      if (window.innerWidth > 768) {
        this.closeMobileMenu()
      }
    }
  },
  
  watch: {
    '$route'() {
      this.handleRouteChange()
    }
  },
  
  async mounted() {
    try {
      // Initialize the application
      await this.initializeApp()
      
      // Load user data if logged in
      if (this.isAuthenticated) {
        await this.loadUserData()
        await this.loadCartData()
      }
      
      // Add event listeners
      window.addEventListener('resize', this.handleResize)
      
      // Handle initial route
      this.handleRouteChange()
      
    } catch (error) {
      console.error('Error initializing app:', error)
      this.$store.dispatch('showNotification', {
        type: 'error',
        message: 'Failed to initialize application. Please refresh the page.'
      })
    }
  },
  
  beforeUnmount() {
    // Clean up event listeners
    window.removeEventListener('resize', this.handleResize)
  }
}
</script>

<style>
/* Global Styles */
@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap');

:root {
  --primary-color: #27ae60;
  --primary-dark: #219a52;
  --secondary-color: #2ecc71;
  --accent-color: #f39c12;
  --danger-color: #e74c3c;
  --warning-color: #f1c40f;
  --info-color: #3498db;
  --success-color: #2ecc71;
  
  --text-dark: #2c3e50;
  --text-light: #7f8c8d;
  --text-muted: #95a5a6;
  
  --background-light: #f8f9fa;
  --background-white: #ffffff;
  --background-dark: #2c3e50;
  
  --border-color: #dee2e6;
  --border-light: #e9ecef;
  
  --shadow-sm: 0 2px 4px rgba(0,0,0,0.1);
  --shadow-md: 0 4px 8px rgba(0,0,0,0.1);
  --shadow-lg: 0 8px 16px rgba(0,0,0,0.1);
  
  --border-radius: 8px;
  --border-radius-sm: 4px;
  --border-radius-lg: 12px;
  
  --transition: all 0.3s ease;
  --transition-fast: all 0.15s ease;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Poppins', sans-serif;
  line-height: 1.6;
  color: var(--text-dark);
  background-color: var(--background-light);
  overflow-x: hidden;
}

#app {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.main-layout {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
}

.main-content {
  flex: 1;
  padding-top: 80px; /* Account for fixed navbar */
}

.admin-layout {
  min-height: 100vh;
}

/* Utility Classes */
.text-primary { color: var(--primary-color) !important; }
.text-secondary { color: var(--secondary-color) !important; }
.text-accent { color: var(--accent-color) !important; }
.text-muted { color: var(--text-muted) !important; }

.bg-primary { background-color: var(--primary-color) !important; }
.bg-secondary { background-color: var(--secondary-color) !important; }
.bg-light { background-color: var(--background-light) !important; }

.btn-primary {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
  transition: var(--transition);
}

.btn-primary:hover {
  background-color: var(--primary-dark);
  border-color: var(--primary-dark);
  transform: translateY(-2px);
}

.card {
  border: none;
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-sm);
  transition: var(--transition);
}

.card:hover {
  box-shadow: var(--shadow-md);
  transform: translateY(-2px);
}

/* Responsive Design */
@media (max-width: 768px) {
  .main-content {
    padding-top: 70px;
  }
}

/* Scrollbar Styling */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--background-light);
}

::-webkit-scrollbar-thumb {
  background: var(--text-light);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--text-dark);
}

/* Animation Classes */
.fade-enter-active, .fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from, .fade-leave-to {
  opacity: 0;
}

.slide-enter-active, .slide-leave-active {
  transition: transform 0.3s ease;
}

.slide-enter-from {
  transform: translateX(-100%);
}

.slide-leave-to {
  transform: translateX(100%);
}
</style>
