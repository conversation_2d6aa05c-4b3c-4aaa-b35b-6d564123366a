import React, { Component } from 'react';
import { Container, Row, Col, Form, FormGroup, Label, Input, Button, Card, CardBody, CardTitle, Table } from 'reactstrap';
import { Link } from 'react-router-dom';
import Navigation from '../../Components/Navigation/Navigation';
import Footer from '../../Components/Footer/Footer';

class OrderOnline extends Component {
  constructor(props) {
    super(props);
    this.state = {
      name: '',
      email: '',
      phone: '',
      address: '',
      city: '',
      zipCode: '',
      orderType: 'delivery',
      paymentMethod: 'credit',
      promoCode: '',
      orderPlaced: false
    };
  }

  handleInputChange = (e) => {
    this.setState({
      [e.target.name]: e.target.value
    });
  }

  handleSubmit = (e) => {
    e.preventDefault();
    // Here you would typically send the order to your backend
    this.setState({ orderPlaced: true });
  }

  calculateTotal = () => {
    let total = 0;
    this.props.data.forEach(item => {
      total += item.price * item.counter;
    });
    return total.toFixed(2);
  }

  render() {
    const { data, count } = this.props;
    const { orderPlaced } = this.state;
    
    if (data.length === 0) {
      return (
        <div>
          <Navigation cartCount={count} />
          <Container className="py-5 text-center">
            <h1>Order Online</h1>
            <p className="lead mb-4">Your cart is empty. Please add items to your cart before checking out.</p>
            <Button 
              color="success" 
              size="lg" 
              tag={Link} 
              to="/menu"
            >
              Browse Menu
            </Button>
          </Container>
          <Footer />
        </div>
      );
    }

    if (orderPlaced) {
      return (
        <div>
          <Navigation cartCount={0} />
          <Container className="py-5 text-center">
            <div className="mb-4">
              <i className="fas fa-check-circle text-success" style={{ fontSize: '5rem' }}></i>
            </div>
            <h1>Thank You for Your Order!</h1>
            <p className="lead mb-4">Your order has been placed successfully.</p>
            <p>We've sent a confirmation email to {this.state.email}.</p>
            <p>Your order will be {this.state.orderType === 'delivery' ? 'delivered' : 'ready for pickup'} soon.</p>
            <Button 
              color="success" 
              size="lg" 
              tag={Link} 
              to="/"
              className="mt-4"
            >
              Return to Home
            </Button>
          </Container>
          <Footer />
        </div>
      );
    }

    return (
      <div>
        <Navigation cartCount={count} />
        
        <Container className="py-5">
          <h1 className="text-center mb-5">Checkout</h1>
          
          <Row>
            <Col md={8}>
              <Card className="mb-4">
                <CardBody>
                  <CardTitle tag="h3">Delivery Information</CardTitle>
                  <Form onSubmit={this.handleSubmit}>
                    <Row>
                      <Col md={6}>
                        <FormGroup>
                          <Label for="name">Full Name</Label>
                          <Input 
                            type="text" 
                            name="name" 
                            id="name" 
                            placeholder="Enter your full name" 
                            value={this.state.name}
                            onChange={this.handleInputChange}
                            required
                          />
                        </FormGroup>
                      </Col>
                      <Col md={6}>
                        <FormGroup>
                          <Label for="email">Email</Label>
                          <Input 
                            type="email" 
                            name="email" 
                            id="email" 
                            placeholder="Enter your email" 
                            value={this.state.email}
                            onChange={this.handleInputChange}
                            required
                          />
                        </FormGroup>
                      </Col>
                    </Row>
                    <FormGroup>
                      <Label for="phone">Phone Number</Label>
                      <Input 
                        type="tel" 
                        name="phone" 
                        id="phone" 
                        placeholder="Enter your phone number" 
                        value={this.state.phone}
                        onChange={this.handleInputChange}
                        required
                      />
                    </FormGroup>
                    <FormGroup>
                      <Label for="orderType">Order Type</Label>
                      <div>
                        <FormGroup check inline>
                          <Label check>
                            <Input 
                              type="radio" 
                              name="orderType" 
                              value="delivery" 
                              checked={this.state.orderType === 'delivery'}
                              onChange={this.handleInputChange}
                            /> Delivery
                          </Label>
                        </FormGroup>
                        <FormGroup check inline>
                          <Label check>
                            <Input 
                              type="radio" 
                              name="orderType" 
                              value="pickup" 
                              checked={this.state.orderType === 'pickup'}
                              onChange={this.handleInputChange}
                            /> Pickup
                          </Label>
                        </FormGroup>
                      </div>
                    </FormGroup>
                    
                    {this.state.orderType === 'delivery' && (
                      <>
                        <FormGroup>
                          <Label for="address">Delivery Address</Label>
                          <Input 
                            type="text" 
                            name="address" 
                            id="address" 
                            placeholder="Enter your street address" 
                            value={this.state.address}
                            onChange={this.handleInputChange}
                            required={this.state.orderType === 'delivery'}
                          />
                        </FormGroup>
                        <Row>
                          <Col md={6}>
                            <FormGroup>
                              <Label for="city">City</Label>
                              <Input 
                                type="text" 
                                name="city" 
                                id="city" 
                                placeholder="Enter your city" 
                                value={this.state.city}
                                onChange={this.handleInputChange}
                                required={this.state.orderType === 'delivery'}
                              />
                            </FormGroup>
                          </Col>
                          <Col md={6}>
                            <FormGroup>
                              <Label for="zipCode">ZIP Code</Label>
                              <Input 
                                type="text" 
                                name="zipCode" 
                                id="zipCode" 
                                placeholder="Enter your ZIP code" 
                                value={this.state.zipCode}
                                onChange={this.handleInputChange}
                                required={this.state.orderType === 'delivery'}
                              />
                            </FormGroup>
                          </Col>
                        </Row>
                      </>
                    )}
                    
                    <FormGroup>
                      <Label for="paymentMethod">Payment Method</Label>
                      <div>
                        <FormGroup check inline>
                          <Label check>
                            <Input 
                              type="radio" 
                              name="paymentMethod" 
                              value="credit" 
                              checked={this.state.paymentMethod === 'credit'}
                              onChange={this.handleInputChange}
                            /> Credit Card
                          </Label>
                        </FormGroup>
                        <FormGroup check inline>
                          <Label check>
                            <Input 
                              type="radio" 
                              name="paymentMethod" 
                              value="cash" 
                              checked={this.state.paymentMethod === 'cash'}
                              onChange={this.handleInputChange}
                            /> Cash on Delivery
                          </Label>
                        </FormGroup>
                      </div>
                    </FormGroup>
                    
                    <FormGroup>
                      <Label for="promoCode">Promo Code</Label>
                      <Input 
                        type="text" 
                        name="promoCode" 
                        id="promoCode" 
                        placeholder="Enter promo code (if any)" 
                        value={this.state.promoCode}
                        onChange={this.handleInputChange}
                      />
                    </FormGroup>
                    
                    <Button color="success" size="lg" block type="submit">
                      Place Order
                    </Button>
                  </Form>
                </CardBody>
              </Card>
            </Col>
            
            <Col md={4}>
              <Card>
                <CardBody>
                  <CardTitle tag="h3">Order Summary</CardTitle>
                  <Table>
                    <thead>
                      <tr>
                        <th>Item</th>
                        <th>Qty</th>
                        <th>Price</th>
                      </tr>
                    </thead>
                    <tbody>
                      {data.map((item, index) => (
                        <tr key={index}>
                          <td>{item.head}</td>
                          <td>{item.counter}</td>
                          <td>${(item.price * item.counter).toFixed(2)}</td>
                        </tr>
                      ))}
                    </tbody>
                  </Table>
                  <hr />
                  <Table>
                    <tbody>
                      <tr>
                        <td>Subtotal:</td>
                        <td className="text-right">${this.calculateTotal()}</td>
                      </tr>
                      <tr>
                        <td>Tax (8%):</td>
                        <td className="text-right">${(this.calculateTotal() * 0.08).toFixed(2)}</td>
                      </tr>
                      {this.state.orderType === 'delivery' && (
                        <tr>
                          <td>Delivery Fee:</td>
                          <td className="text-right">$3.99</td>
                        </tr>
                      )}
                      <tr>
                        <th>Total:</th>
                        <th className="text-right">
                          ${(
                            parseFloat(this.calculateTotal()) + 
                            parseFloat(this.calculateTotal()) * 0.08 + 
                            (this.state.orderType === 'delivery' ? 3.99 : 0)
                          ).toFixed(2)}
                        </th>
                      </tr>
                    </tbody>
                  </Table>
                  <Button 
                    color="link" 
                    block 
                    tag={Link} 
                    to="/cart"
                  >
                    Edit Cart
                  </Button>
                </CardBody>
              </Card>
            </Col>
          </Row>
        </Container>
        
        <Footer />
      </div>
    );
  }
}

export default OrderOnline;
