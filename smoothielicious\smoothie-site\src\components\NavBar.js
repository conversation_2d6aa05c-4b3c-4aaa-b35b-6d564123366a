import React, { useState } from 'react';
import { Link } from 'react-router-dom';
import {
  Collapse,
  Navbar,
  Navbar<PERSON>oggler,
  Navbar<PERSON>rand,
  Nav,
  NavItem,
  NavLink,
  Container,
  Badge,
  Button,
  UncontrolledDropdown,
  DropdownToggle,
  DropdownMenu,
  DropdownItem
} from 'reactstrap';

const NavBar = ({ cartCount, isLoggedIn, username, onLogout }) => {
  const [isOpen, setIsOpen] = useState(false);

  const toggle = () => setIsOpen(!isOpen);

  return (
    <Navbar color="light" light expand="md" className="mb-4 sticky-top">
      <Container>
        <NavbarBrand tag={Link} to="/">
          <img 
            src="/logo.png" 
            alt="Smoothielicious Logo" 
            height="40" 
            className="d-inline-block align-top mr-2" 
          />
          Smoothielicious
        </NavbarBrand>
        <NavbarToggler onClick={toggle} />
        <Collapse isOpen={isOpen} navbar>
          <Nav className="ml-auto" navbar>
            <NavItem>
              <NavLink tag={Link} to="/">Home</NavLink>
            </NavItem>
            <NavItem>
              <NavLink tag={Link} to="/menu">Menu</NavLink>
            </NavItem>
            <NavItem>
              <NavLink tag={Link} to="/offers">Offers</NavLink>
            </NavItem>
            <NavItem>
              <NavLink tag={Link} to="/order">Order Online</NavLink>
            </NavItem>
            <NavItem>
              <NavLink tag={Link} to="/cart">
                <i className="fas fa-shopping-cart"></i> Cart
                {cartCount > 0 && (
                  <Badge color="success" pill className="ml-1">
                    {cartCount}
                  </Badge>
                )}
              </NavLink>
            </NavItem>
            {isLoggedIn ? (
              <UncontrolledDropdown nav inNavbar>
                <DropdownToggle nav caret>
                  <i className="fas fa-user mr-1"></i> {username}
                </DropdownToggle>
                <DropdownMenu right>
                  <DropdownItem tag={Link} to="/profile">
                    <i className="fas fa-user-circle mr-2"></i> My Profile
                  </DropdownItem>
                  <DropdownItem tag={Link} to="/orders">
                    <i className="fas fa-list-alt mr-2"></i> My Orders
                  </DropdownItem>
                  <DropdownItem divider />
                  <DropdownItem onClick={onLogout}>
                    <i className="fas fa-sign-out-alt mr-2"></i> Logout
                  </DropdownItem>
                </DropdownMenu>
              </UncontrolledDropdown>
            ) : (
              <NavItem>
                <Button color="success" tag={Link} to="/login" className="ml-2">
                  <i className="fas fa-sign-in-alt mr-1"></i> Login
                </Button>
              </NavItem>
            )}
          </Nav>
        </Collapse>
      </Container>
    </Navbar>
  );
};

export default NavBar;
