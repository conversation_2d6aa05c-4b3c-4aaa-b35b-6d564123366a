import React, { Component } from 'react';
import { Container, Row, Col, Card, CardImg, CardBody, CardTitle, CardText, Button, Nav, NavItem, NavLink } from 'reactstrap';
import Navigation from '../../Components/Navigation/Navigation';
import Footer from '../../Components/Footer/Footer';

class Menu extends Component {
  constructor(props) {
    super(props);
    this.state = {
      activeCategory: 1
    };
  }

  setActiveCategory = (categoryId) => {
    this.setState({ activeCategory: categoryId });
  }

  render() {
    const { data, inbox, adding, loaded } = this.props;
    const { activeCategory } = this.state;
    
    if (!loaded || !data || !data.categories) {
      return <div>Loading...</div>;
    }

    const activeItems = data.categories.find(cat => cat.id === activeCategory)?.items || [];

    return (
      <div>
        <Navigation cartCount={inbox} />
        
        <Container className="menu-section">
          <h1 className="text-center mb-5">Our Menu</h1>
          
          {/* Category Navigation */}
          <Nav pills className="justify-content-center mb-5">
            {data.categories.map(category => (
              <NavItem key={category.id}>
                <NavLink
                  className={activeCategory === category.id ? 'active' : ''}
                  onClick={() => this.setActiveCategory(category.id)}
                  style={{ cursor: 'pointer' }}
                >
                  {category.name}
                </NavLink>
              </NavItem>
            ))}
          </Nav>
          
          {/* Menu Items */}
          <Row>
            {activeItems.map(item => (
              <Col md={4} key={item.id} className="mb-4">
                <Card className="menu-item h-100">
                  <CardImg top width="100%" src={item.image} alt={item.name} />
                  <CardBody>
                    <CardTitle tag="h3">{item.name}</CardTitle>
                    <CardText>{item.description}</CardText>
                    <div className="d-flex justify-content-between align-items-center">
                      <span className="price">${item.price.toFixed(2)}</span>
                      <Button 
                        color="success" 
                        onClick={() => adding({
                          head: item.name,
                          desc: item.description,
                          price: item.price,
                          counter: 1,
                          image: item.image
                        })}
                      >
                        Add to Cart
                      </Button>
                    </div>
                  </CardBody>
                </Card>
              </Col>
            ))}
          </Row>
        </Container>
        
        <Footer />
      </div>
    );
  }
}

export default Menu;
