// Food/Smoothie Model
import db from "../config/database.js";

// Get all food items
export const getFoods = (result) => {
    db.query("SELECT * FROM food ORDER BY food_category, food_name", (err, results) => {
        if (err) {
            console.error("Error fetching foods:", err);
            result(err, null);
        } else {
            result(null, results);
        }
    });
};

// Get single food item by ID
export const getFoodById = (id, result) => {
    db.query("SELECT * FROM food WHERE food_id = ?", [id], (err, results) => {
        if (err) {
            console.error("Error fetching food by ID:", err);
            result(err, null);
        } else {
            result(null, results[0]);
        }
    });
};

// Get foods by category
export const getFoodsByCategory = (category, result) => {
    db.query("SELECT * FROM food WHERE food_category = ? ORDER BY food_name", [category], (err, results) => {
        if (err) {
            console.error("Error fetching foods by category:", err);
            result(err, null);
        } else {
            result(null, results);
        }
    });
};

// Insert new food item
export const insertFood = (data, result) => {
    db.query("INSERT INTO food SET ?", data, (err, results) => {
        if (err) {
            console.error("Error inserting food:", err);
            result(err, null);
        } else {
            result(null, results);
        }
    });
};

// Update food item
export const updateFood = (id, data, result) => {
    db.query("UPDATE food SET ? WHERE food_id = ?", [data, id], (err, results) => {
        if (err) {
            console.error("Error updating food:", err);
            result(err, null);
        } else {
            result(null, results);
        }
    });
};

// Delete food item
export const deleteFood = (id, result) => {
    db.query("DELETE FROM food WHERE food_id = ?", [id], (err, results) => {
        if (err) {
            console.error("Error deleting food:", err);
            result(err, null);
        } else {
            result(null, results);
        }
    });
};

// Search foods by name
export const searchFoods = (searchTerm, result) => {
    const searchPattern = `%${searchTerm}%`;
    db.query(
        "SELECT * FROM food WHERE food_name LIKE ? OR food_desc LIKE ? ORDER BY food_name", 
        [searchPattern, searchPattern], 
        (err, results) => {
            if (err) {
                console.error("Error searching foods:", err);
                result(err, null);
            } else {
                result(null, results);
            }
        }
    );
};
