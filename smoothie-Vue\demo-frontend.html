<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Smoothie Vue - Demo Frontend</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <style>
        :root {
            --primary-color: #27ae60;
            --primary-dark: #219a52;
            --secondary-color: #2ecc71;
            --accent-color: #f39c12;
            --text-dark: #2c3e50;
            --text-light: #7f8c8d;
            --background-light: #f8f9fa;
        }
        
        body {
            font-family: 'Poppins', sans-serif;
            background-color: var(--background-light);
        }
        
        .navbar-brand {
            font-weight: 700;
            font-size: 1.5rem;
        }
        
        .hero-section {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            min-height: 80vh;
            display: flex;
            align-items: center;
        }
        
        .hero-title {
            font-size: 3rem;
            font-weight: 700;
            color: var(--text-dark);
        }
        
        .text-primary {
            color: var(--primary-color) !important;
        }
        
        .btn-primary {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
            font-weight: 500;
        }
        
        .btn-primary:hover {
            background-color: var(--primary-dark);
            border-color: var(--primary-dark);
            transform: translateY(-2px);
        }
        
        .food-card {
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            height: 100%;
        }
        
        .food-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }
        
        .food-image {
            height: 200px;
            background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 3rem;
        }
        
        .food-badge {
            position: absolute;
            top: 10px;
            right: 10px;
            background: var(--accent-color);
            color: white;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.75rem;
            font-weight: 500;
            text-transform: capitalize;
        }
        
        .cart-item {
            background: white;
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 0.5rem;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 9999;
            min-width: 300px;
        }
        
        .loading {
            display: none;
        }
        
        .loading.show {
            display: block;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-light bg-white shadow-sm">
        <div class="container">
            <a class="navbar-brand text-primary" href="#home">
                <i class="fas fa-leaf me-2"></i>Smoothie Vue
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="#home">Home</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#menu">Menu</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#about">About</a>
                    </li>
                </ul>
                
                <div class="d-flex align-items-center">
                    <button class="btn btn-outline-primary me-2 position-relative" onclick="toggleCart()">
                        <i class="fas fa-shopping-cart"></i>
                        <span class="d-none d-md-inline ms-1">Cart</span>
                        <span id="cart-badge" class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger" style="display: none;">
                            0
                        </span>
                    </button>
                    <button class="btn btn-primary" onclick="showLogin()">
                        <i class="fas fa-sign-in-alt me-1"></i>Login
                    </button>
                </div>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main>
        <!-- Hero Section -->
        <section id="home" class="hero-section">
            <div class="container">
                <div class="row align-items-center">
                    <div class="col-lg-6">
                        <h1 class="hero-title">
                            Fresh & Delicious
                            <span class="text-primary">Smoothies</span>
                        </h1>
                        <p class="lead text-muted mb-4">
                            Made with premium ingredients and packed with nutrients. 
                            Start your healthy journey with our amazing smoothie collection.
                        </p>
                        <div class="d-flex gap-3">
                            <button class="btn btn-primary btn-lg" onclick="scrollToMenu()">
                                <i class="fas fa-utensils me-2"></i>Browse Menu
                            </button>
                            <button class="btn btn-outline-primary btn-lg" onclick="testAPI()">
                                <i class="fas fa-flask me-2"></i>Test API
                            </button>
                        </div>
                    </div>
                    <div class="col-lg-6 text-center">
                        <div class="hero-image">
                            <i class="fas fa-glass-whiskey text-primary" style="font-size: 8rem;"></i>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Menu Section -->
        <section id="menu" class="py-5">
            <div class="container">
                <div class="text-center mb-5">
                    <h2 class="display-4 fw-bold">Our Menu</h2>
                    <p class="lead text-muted">Fresh smoothies made with love</p>
                    <button class="btn btn-primary" onclick="loadMenu()">
                        <i class="fas fa-sync-alt me-2"></i>Load Menu from API
                    </button>
                </div>
                
                <div id="loading" class="text-center loading">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <p class="mt-2">Loading delicious smoothies...</p>
                </div>
                
                <div id="menu-items" class="row">
                    <!-- Menu items will be loaded here -->
                </div>
            </div>
        </section>

        <!-- About Section -->
        <section id="about" class="py-5 bg-light">
            <div class="container">
                <div class="row">
                    <div class="col-lg-8 mx-auto text-center">
                        <h2 class="display-4 fw-bold mb-4">About Smoothie Vue</h2>
                        <p class="lead">
                            We're passionate about creating the perfect blend of taste and nutrition. 
                            Our smoothies are made with fresh, locally-sourced ingredients and packed 
                            with vitamins and minerals to fuel your active lifestyle.
                        </p>
                        <div class="row mt-5">
                            <div class="col-md-4">
                                <i class="fas fa-leaf text-primary mb-3" style="font-size: 3rem;"></i>
                                <h4>Fresh Ingredients</h4>
                                <p>Locally sourced, organic when possible</p>
                            </div>
                            <div class="col-md-4">
                                <i class="fas fa-heart text-primary mb-3" style="font-size: 3rem;"></i>
                                <h4>Healthy & Nutritious</h4>
                                <p>Packed with vitamins and antioxidants</p>
                            </div>
                            <div class="col-md-4">
                                <i class="fas fa-clock text-primary mb-3" style="font-size: 3rem;"></i>
                                <h4>Quick Service</h4>
                                <p>Fast preparation, fresh delivery</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- Cart Sidebar -->
    <div id="cart-sidebar" class="position-fixed top-0 end-0 h-100 bg-white shadow-lg" style="width: 400px; transform: translateX(100%); transition: transform 0.3s ease; z-index: 1050;">
        <div class="p-4 border-bottom">
            <div class="d-flex justify-content-between align-items-center">
                <h5 class="mb-0">Shopping Cart</h5>
                <button class="btn-close" onclick="toggleCart()"></button>
            </div>
        </div>
        <div class="p-4">
            <div id="cart-items">
                <div class="text-center text-muted">
                    <i class="fas fa-shopping-cart mb-3" style="font-size: 3rem;"></i>
                    <p>Your cart is empty</p>
                    <button class="btn btn-primary btn-sm" onclick="scrollToMenu(); toggleCart();">
                        Browse Menu
                    </button>
                </div>
            </div>
            <div id="cart-total" class="border-top pt-3 mt-3" style="display: none;">
                <div class="d-flex justify-content-between">
                    <strong>Total: $<span id="total-amount">0.00</span></strong>
                </div>
                <button class="btn btn-primary w-100 mt-3" onclick="checkout()">
                    <i class="fas fa-credit-card me-2"></i>Checkout
                </button>
            </div>
        </div>
    </div>

    <!-- Notification Container -->
    <div id="notification-container"></div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Application State
        let cart = [];
        let menuItems = [];
        
        // API Base URL
        const API_BASE = 'http://localhost:8001/api';
        
        // Utility Functions
        function showNotification(message, type = 'success') {
            const container = document.getElementById('notification-container');
            const notification = document.createElement('div');
            notification.className = `alert alert-${type} alert-dismissible fade show notification`;
            notification.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            container.appendChild(notification);
            
            setTimeout(() => {
                notification.remove();
            }, 5000);
        }
        
        function formatCurrency(amount) {
            return new Intl.NumberFormat('en-US', {
                style: 'currency',
                currency: 'USD'
            }).format(amount);
        }
        
        // Navigation Functions
        function scrollToMenu() {
            document.getElementById('menu').scrollIntoView({ behavior: 'smooth' });
        }
        
        function testAPI() {
            window.open('test-api.html', '_blank');
        }
        
        function showLogin() {
            showNotification('Login functionality will be implemented in the full Vue.js version!', 'info');
        }
        
        // Menu Functions
        async function loadMenu() {
            const loading = document.getElementById('loading');
            const menuContainer = document.getElementById('menu-items');
            
            loading.classList.add('show');
            
            try {
                // Mock data since we don't have database connected
                const mockMenuItems = [
                    {
                        food_id: 1,
                        food_name: 'Tropical Paradise',
                        food_price: 8.99,
                        food_discount: 0,
                        food_desc: 'Mango, pineapple, coconut milk, and banana',
                        food_status: 'best seller',
                        food_star: 4.8
                    },
                    {
                        food_id: 2,
                        food_name: 'Berry Blast',
                        food_price: 7.99,
                        food_discount: 1.00,
                        food_desc: 'Mixed berries, yogurt, and honey',
                        food_status: 'popular',
                        food_star: 4.7
                    },
                    {
                        food_id: 3,
                        food_name: 'Green Goddess',
                        food_price: 9.99,
                        food_discount: 0,
                        food_desc: 'Spinach, kale, apple, cucumber, and lemon',
                        food_status: 'new',
                        food_star: 4.5
                    },
                    {
                        food_id: 4,
                        food_name: 'Chocolate Protein Power',
                        food_price: 11.99,
                        food_discount: 2.00,
                        food_desc: 'Chocolate protein, banana, almond milk, and peanut butter',
                        food_status: 'best seller',
                        food_star: 4.6
                    }
                ];
                
                menuItems = mockMenuItems;
                renderMenu(mockMenuItems);
                showNotification('Menu loaded successfully!');
                
            } catch (error) {
                console.error('Error loading menu:', error);
                showNotification('Failed to load menu. Please try again.', 'danger');
            } finally {
                loading.classList.remove('show');
            }
        }
        
        function renderMenu(items) {
            const container = document.getElementById('menu-items');
            container.innerHTML = items.map(item => `
                <div class="col-lg-3 col-md-6 mb-4">
                    <div class="food-card">
                        <div class="food-image position-relative">
                            <i class="fas fa-glass-whiskey"></i>
                            ${item.food_status ? `<div class="food-badge">${item.food_status}</div>` : ''}
                        </div>
                        <div class="p-3">
                            <h5 class="fw-bold">${item.food_name}</h5>
                            <p class="text-muted small">${item.food_desc}</p>
                            <div class="d-flex align-items-center mb-2">
                                <div class="text-warning me-2">
                                    ${'★'.repeat(Math.floor(item.food_star))}${'☆'.repeat(5-Math.floor(item.food_star))}
                                </div>
                                <small class="text-muted">${item.food_star}</small>
                            </div>
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    ${item.food_discount > 0 ? 
                                        `<span class="text-decoration-line-through text-muted">${formatCurrency(item.food_price)}</span>
                                         <span class="fw-bold text-primary">${formatCurrency(item.food_price - item.food_discount)}</span>` :
                                        `<span class="fw-bold text-primary">${formatCurrency(item.food_price)}</span>`
                                    }
                                </div>
                                <button class="btn btn-primary btn-sm" onclick="addToCart(${item.food_id})">
                                    <i class="fas fa-plus"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            `).join('');
        }
        
        // Cart Functions
        function addToCart(foodId) {
            const item = menuItems.find(item => item.food_id === foodId);
            if (!item) return;
            
            const existingItem = cart.find(cartItem => cartItem.food_id === foodId);
            if (existingItem) {
                existingItem.quantity += 1;
            } else {
                cart.push({
                    ...item,
                    quantity: 1
                });
            }
            
            updateCartDisplay();
            showNotification(`${item.food_name} added to cart!`);
        }
        
        function removeFromCart(foodId) {
            cart = cart.filter(item => item.food_id !== foodId);
            updateCartDisplay();
            showNotification('Item removed from cart');
        }
        
        function updateQuantity(foodId, quantity) {
            const item = cart.find(item => item.food_id === foodId);
            if (item) {
                item.quantity = Math.max(1, quantity);
                updateCartDisplay();
            }
        }
        
        function updateCartDisplay() {
            const cartItems = document.getElementById('cart-items');
            const cartBadge = document.getElementById('cart-badge');
            const cartTotal = document.getElementById('cart-total');
            const totalAmount = document.getElementById('total-amount');
            
            const totalItems = cart.reduce((sum, item) => sum + item.quantity, 0);
            const total = cart.reduce((sum, item) => sum + ((item.food_price - item.food_discount) * item.quantity), 0);
            
            // Update badge
            if (totalItems > 0) {
                cartBadge.textContent = totalItems;
                cartBadge.style.display = 'block';
            } else {
                cartBadge.style.display = 'none';
            }
            
            // Update cart items
            if (cart.length === 0) {
                cartItems.innerHTML = `
                    <div class="text-center text-muted">
                        <i class="fas fa-shopping-cart mb-3" style="font-size: 3rem;"></i>
                        <p>Your cart is empty</p>
                        <button class="btn btn-primary btn-sm" onclick="scrollToMenu(); toggleCart();">
                            Browse Menu
                        </button>
                    </div>
                `;
                cartTotal.style.display = 'none';
            } else {
                cartItems.innerHTML = cart.map(item => `
                    <div class="cart-item">
                        <div class="d-flex justify-content-between align-items-center">
                            <div class="flex-grow-1">
                                <h6 class="mb-1">${item.food_name}</h6>
                                <small class="text-muted">${formatCurrency(item.food_price - item.food_discount)}</small>
                            </div>
                            <div class="d-flex align-items-center">
                                <button class="btn btn-sm btn-outline-secondary" onclick="updateQuantity(${item.food_id}, ${item.quantity - 1})">-</button>
                                <span class="mx-2">${item.quantity}</span>
                                <button class="btn btn-sm btn-outline-secondary" onclick="updateQuantity(${item.food_id}, ${item.quantity + 1})">+</button>
                                <button class="btn btn-sm btn-outline-danger ms-2" onclick="removeFromCart(${item.food_id})">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                `).join('');
                
                totalAmount.textContent = total.toFixed(2);
                cartTotal.style.display = 'block';
            }
        }
        
        function toggleCart() {
            const sidebar = document.getElementById('cart-sidebar');
            const isOpen = sidebar.style.transform === 'translateX(0px)';
            sidebar.style.transform = isOpen ? 'translateX(100%)' : 'translateX(0px)';
        }
        
        function checkout() {
            if (cart.length === 0) return;
            
            showNotification('Checkout functionality will be implemented in the full Vue.js version!', 'info');
            // In the full version, this would redirect to checkout page
        }
        
        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            showNotification('Welcome to Smoothie Vue! This is a demo version.', 'info');
            loadMenu();
        });
    </script>
</body>
</html>
