<template>
  <div v-if="show" class="confirm-dialog-overlay" @click="handleOverlayClick">
    <div class="confirm-dialog" @click.stop>
      <div class="confirm-header">
        <h5 class="confirm-title">{{ title }}</h5>
        <button class="btn-close" @click="cancel" aria-label="Close"></button>
      </div>
      
      <div class="confirm-body">
        <div class="confirm-icon">
          <i :class="iconClass" :style="{ color: iconColor }"></i>
        </div>
        <p class="confirm-message">{{ message }}</p>
      </div>
      
      <div class="confirm-footer">
        <button 
          class="btn btn-secondary me-2" 
          @click="cancel"
          :disabled="loading"
        >
          {{ cancelText }}
        </button>
        <button 
          :class="['btn', confirmButtonClass]" 
          @click="confirm"
          :disabled="loading"
        >
          <span v-if="loading" class="spinner-border spinner-border-sm me-2"></span>
          {{ confirmText }}
        </button>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ConfirmDialog',
  
  props: {
    show: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: 'Confirm Action'
    },
    message: {
      type: String,
      required: true
    },
    type: {
      type: String,
      default: 'warning',
      validator: value => ['warning', 'danger', 'info', 'success'].includes(value)
    },
    confirmText: {
      type: String,
      default: 'Confirm'
    },
    cancelText: {
      type: String,
      default: 'Cancel'
    },
    loading: {
      type: Boolean,
      default: false
    },
    closeOnOverlay: {
      type: Boolean,
      default: true
    }
  },
  
  emits: ['confirm', 'cancel'],
  
  computed: {
    iconClass() {
      const icons = {
        warning: 'fas fa-exclamation-triangle',
        danger: 'fas fa-exclamation-circle',
        info: 'fas fa-info-circle',
        success: 'fas fa-check-circle'
      }
      return icons[this.type] || icons.warning
    },
    
    iconColor() {
      const colors = {
        warning: '#f1c40f',
        danger: '#e74c3c',
        info: '#3498db',
        success: '#2ecc71'
      }
      return colors[this.type] || colors.warning
    },
    
    confirmButtonClass() {
      const classes = {
        warning: 'btn-warning',
        danger: 'btn-danger',
        info: 'btn-primary',
        success: 'btn-success'
      }
      return classes[this.type] || classes.warning
    }
  },
  
  methods: {
    confirm() {
      this.$emit('confirm')
    },
    
    cancel() {
      this.$emit('cancel')
    },
    
    handleOverlayClick() {
      if (this.closeOnOverlay && !this.loading) {
        this.cancel()
      }
    }
  },
  
  mounted() {
    // Prevent body scroll when dialog is open
    if (this.show) {
      document.body.style.overflow = 'hidden'
    }
  },
  
  beforeUnmount() {
    // Restore body scroll
    document.body.style.overflow = ''
  },
  
  watch: {
    show(newVal) {
      if (newVal) {
        document.body.style.overflow = 'hidden'
      } else {
        document.body.style.overflow = ''
      }
    }
  }
}
</script>

<style scoped>
.confirm-dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  backdrop-filter: blur(2px);
}

.confirm-dialog {
  background: white;
  border-radius: 12px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  max-width: 400px;
  width: 90%;
  max-height: 90vh;
  overflow: hidden;
  animation: dialogSlideIn 0.3s ease-out;
}

.confirm-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem 1.5rem 0 1.5rem;
}

.confirm-title {
  margin: 0;
  font-weight: 600;
  color: var(--text-dark, #2c3e50);
}

.btn-close {
  background: none;
  border: none;
  font-size: 1.25rem;
  cursor: pointer;
  opacity: 0.7;
  padding: 0;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.btn-close:hover {
  opacity: 1;
}

.confirm-body {
  padding: 1.5rem;
  text-align: center;
}

.confirm-icon {
  margin-bottom: 1rem;
}

.confirm-icon i {
  font-size: 3rem;
}

.confirm-message {
  margin: 0;
  color: var(--text-secondary, #7f8c8d);
  line-height: 1.5;
}

.confirm-footer {
  padding: 0 1.5rem 1.5rem 1.5rem;
  display: flex;
  justify-content: flex-end;
}

.btn {
  padding: 0.5rem 1rem;
  border-radius: 6px;
  font-weight: 500;
  border: 1px solid transparent;
  cursor: pointer;
  transition: all 0.15s ease-in-out;
  display: inline-flex;
  align-items: center;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn-secondary {
  background-color: #6c757d;
  border-color: #6c757d;
  color: white;
}

.btn-secondary:hover:not(:disabled) {
  background-color: #5a6268;
  border-color: #545b62;
}

.btn-primary {
  background-color: #007bff;
  border-color: #007bff;
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background-color: #0056b3;
  border-color: #004085;
}

.btn-warning {
  background-color: #ffc107;
  border-color: #ffc107;
  color: #212529;
}

.btn-warning:hover:not(:disabled) {
  background-color: #e0a800;
  border-color: #d39e00;
}

.btn-danger {
  background-color: #dc3545;
  border-color: #dc3545;
  color: white;
}

.btn-danger:hover:not(:disabled) {
  background-color: #c82333;
  border-color: #bd2130;
}

.btn-success {
  background-color: #28a745;
  border-color: #28a745;
  color: white;
}

.btn-success:hover:not(:disabled) {
  background-color: #218838;
  border-color: #1e7e34;
}

.spinner-border {
  width: 1rem;
  height: 1rem;
  border: 0.125em solid currentColor;
  border-right-color: transparent;
  border-radius: 50%;
  animation: spinner-border 0.75s linear infinite;
}

.spinner-border-sm {
  width: 0.875rem;
  height: 0.875rem;
  border-width: 0.125em;
}

@keyframes dialogSlideIn {
  from {
    opacity: 0;
    transform: translateY(-50px) scale(0.9);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes spinner-border {
  to {
    transform: rotate(360deg);
  }
}
</style>
