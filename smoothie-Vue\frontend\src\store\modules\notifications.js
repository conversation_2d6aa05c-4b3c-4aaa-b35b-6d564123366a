// Notifications Store Module
const state = {
  notifications: []
}

const getters = {
  notifications: state => state.notifications,
  hasNotifications: state => state.notifications.length > 0
}

const mutations = {
  ADD_NOTIFICATION(state, notification) {
    state.notifications.push({
      id: Date.now() + Math.random(),
      timestamp: new Date().toISOString(),
      ...notification
    })
  },
  
  REMOVE_NOTIFICATION(state, id) {
    state.notifications = state.notifications.filter(notification => notification.id !== id)
  },
  
  CLEAR_ALL_NOTIFICATIONS(state) {
    state.notifications = []
  }
}

const actions = {
  // Show notification
  showNotification({ commit }, notification) {
    const {
      type = 'info',
      title = '',
      message = '',
      duration = 5000,
      persistent = false
    } = notification
    
    const id = Date.now() + Math.random()
    
    commit('ADD_NOTIFICATION', {
      id,
      type,
      title,
      message,
      duration,
      persistent
    })
    
    // Auto-remove notification after duration (unless persistent)
    if (!persistent && duration > 0) {
      setTimeout(() => {
        commit('REMOVE_NOTIFICATION', id)
      }, duration)
    }
    
    return id
  },
  
  // Remove specific notification
  removeNotification({ commit }, id) {
    commit('REMOVE_NOTIFICATION', id)
  },
  
  // Clear all notifications
  clearAllNotifications({ commit }) {
    commit('CLEAR_ALL_NOTIFICATIONS')
  },
  
  // Convenience methods for different notification types
  showSuccess({ dispatch }, message, options = {}) {
    return dispatch('showNotification', {
      type: 'success',
      message,
      duration: 3000,
      ...options
    })
  },
  
  showError({ dispatch }, message, options = {}) {
    return dispatch('showNotification', {
      type: 'error',
      message,
      duration: 5000,
      ...options
    })
  },
  
  showWarning({ dispatch }, message, options = {}) {
    return dispatch('showNotification', {
      type: 'warning',
      message,
      duration: 4000,
      ...options
    })
  },
  
  showInfo({ dispatch }, message, options = {}) {
    return dispatch('showNotification', {
      type: 'info',
      message,
      duration: 3000,
      ...options
    })
  }
}

export default {
  namespaced: true,
  state,
  getters,
  mutations,
  actions
}
