{"name": "smoothielicious", "version": "1.0.0", "description": "Fresh & Healthy Smoothies", "main": "index.js", "scripts": {"start": "node server.js", "build": "webpack --mode production", "test": "echo \"Error: no test specified\" && exit 1", "dev": "webpack serve --mode development --open", "static": "http-server -o"}, "keywords": ["smoothie", "healthy", "food", "restaurant"], "author": "Smoothielicious", "license": "ISC", "dependencies": {"axios": "^1.6.7", "bootstrap": "^5.3.3", "express": "^5.1.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.22.3", "reactstrap": "^9.2.2"}, "devDependencies": {"@babel/core": "^7.27.1", "@babel/preset-env": "^7.27.2", "@babel/preset-react": "^7.27.1", "babel-loader": "^9.2.1", "css-loader": "^6.11.0", "file-loader": "^6.2.0", "html-webpack-plugin": "^5.6.3", "http-server": "^14.1.1", "style-loader": "^3.3.4", "webpack": "^5.99.8", "webpack-cli": "^5.1.4", "webpack-dev-server": "^5.2.1"}}