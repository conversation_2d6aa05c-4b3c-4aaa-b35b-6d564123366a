// Smoothie Vue Restaurant Ordering System Backend
// Main server entry point

import express from "express";
import bodyParser from "body-parser";
import cors from "cors";
import dotenv from "dotenv";
import path from "path";

// Import routes
import router from "./routes/routes.js";

// Load environment variables
dotenv.config();

// Get directory path
const __dirname = path.resolve();

// Initialize express app
const app = express();

// Middleware
app.use(bodyParser.json());
app.use(bodyParser.urlencoded({ extended: true }));

// CORS configuration
const corsOptions = {
  origin: process.env.FRONTEND_URL || "http://localhost:8080",
  credentials: true,
  optionsSuccessStatus: 200
};
app.use(cors(corsOptions));

// API routes
app.use("/api", router);

// Health check endpoint
app.get("/api/health", (req, res) => {
  res.json({ 
    message: "Smoothie Vue API is running!", 
    timestamp: new Date().toISOString(),
    environment: process.env.NODE_ENV || "development"
  });
});

// Serve static files in production
if (process.env.NODE_ENV === 'production') {
  app.use(express.static(path.join(__dirname, './dist')));
  
  app.get('/*', (req, res) => {
    res.sendFile(path.join(__dirname, './dist/index.html'));
  });
}

// Error handling middleware
app.use((err, req, res, next) => {
  console.error(err.stack);
  res.status(500).json({ 
    message: "Something went wrong!",
    error: process.env.NODE_ENV === 'development' ? err.message : {}
  });
});

// 404 handler
app.use((req, res) => {
  res.status(404).json({ message: "Route not found" });
});

// Start server
const PORT = process.env.PORT || 8001;
app.listen(PORT, () => {
  console.log(`🚀 Smoothie Vue server is running on port ${PORT}`);
  console.log(`📊 Environment: ${process.env.NODE_ENV || "development"}`);
  console.log(`🔗 API Health Check: http://localhost:${PORT}/api/health`);
});
