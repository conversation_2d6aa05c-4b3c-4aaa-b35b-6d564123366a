// Main Routes Configuration
import express from "express";

// Import controllers
import {
    showFoods,
    showFoodById,
    showFoodsByCategory,
    createFood,
    updateFoodItem,
    deleteFoodItem,
    searchFoodItems
} from "../controllers/food.js";

import {
    showAllUsers,
    showAUser,
    showUserById,
    createAccount,
    updateUserAccount,
    deleteUserAccount,
    loginUser
} from "../controllers/user.js";

import {
    allItems,
    getItem,
    addItems,
    updateItem,
    deleteItem,
    deleteItems,
    getCartSummaryData
} from "../controllers/cart.js";

// Initialize router
const router = express.Router();

// ========================== FOOD ROUTES ==========================
// Get all foods
router.get("/foods", showFoods);

// Search foods
router.get("/foods/search", searchFoodItems);

// Get foods by category
router.get("/foods/category/:category", showFoodsByCategory);

// Get single food by ID
router.get("/foods/:id", showFoodById);

// Create new food (admin only)
router.post("/foods", createFood);

// Update food (admin only)
router.put("/foods/:id", updateFoodItem);

// Delete food (admin only)
router.delete("/foods/:id", deleteFoodItem);

// ========================== USER ROUTES ==========================
// Get all users (admin only)
router.get("/users", showAllUsers);

// Get user by email (for login)
router.get("/users/email/:email", showAUser);

// Get user by ID
router.get("/users/:id", showUserById);

// Create new user account
router.post("/users", createAccount);

// Login user
router.post("/users/login", loginUser);

// Update user
router.put("/users/:id", updateUserAccount);

// Delete user
router.delete("/users/:id", deleteUserAccount);

// ========================== CART ROUTES ==========================
// Get all cart items for a user
router.get("/cart/:id", allItems);

// Get cart summary for a user
router.get("/cart/:id/summary", getCartSummaryData);

// Get specific cart item
router.get("/cart/:user_id/:food_id", getItem);

// Add item to cart
router.post("/cart", addItems);

// Update cart item quantity
router.put("/cart", updateItem);

// Delete specific item from cart
router.delete("/cart/:user_id/:food_id", deleteItem);

// Clear all items from user's cart
router.delete("/cart/:id", deleteItems);

// ========================== UTILITY ROUTES ==========================
// Get available categories
router.get("/categories", (req, res) => {
    // This could be dynamic from database in the future
    const categories = [
        "smoothie",
        "bowl",
        "juice",
        "protein",
        "add-ons"
    ];
    
    res.json({
        success: true,
        data: categories
    });
});

// Get menu statistics (admin)
router.get("/stats/menu", (req, res) => {
    // This would typically query the database for real stats
    res.json({
        success: true,
        data: {
            total_items: 0,
            categories: 5,
            active_items: 0,
            last_updated: new Date().toISOString()
        }
    });
});

export default router;
