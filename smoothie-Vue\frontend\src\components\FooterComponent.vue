<template>
  <footer class="footer bg-dark text-white mt-auto">
    <div class="container py-5">
      <div class="row">
        <!-- Brand Section -->
        <div class="col-lg-4 col-md-6 mb-4">
          <div class="d-flex align-items-center mb-3">
            <i class="fas fa-leaf text-primary me-2 fs-3"></i>
            <h5 class="mb-0 text-white">Smoothie Vue</h5>
          </div>
          <p class="text-light">
            Fresh, healthy, and delicious smoothies made with premium ingredients. 
            Your wellness journey starts here.
          </p>
          <div class="social-links">
            <a href="#" class="text-light me-3" aria-label="Facebook">
              <i class="fab fa-facebook-f"></i>
            </a>
            <a href="#" class="text-light me-3" aria-label="Instagram">
              <i class="fab fa-instagram"></i>
            </a>
            <a href="#" class="text-light me-3" aria-label="Twitter">
              <i class="fab fa-twitter"></i>
            </a>
            <a href="#" class="text-light" aria-label="YouTube">
              <i class="fab fa-youtube"></i>
            </a>
          </div>
        </div>

        <!-- Quick Links -->
        <div class="col-lg-2 col-md-6 mb-4">
          <h6 class="text-primary mb-3">Quick Links</h6>
          <ul class="list-unstyled">
            <li class="mb-2">
              <router-link to="/" class="text-light text-decoration-none">Home</router-link>
            </li>
            <li class="mb-2">
              <router-link to="/menu" class="text-light text-decoration-none">Menu</router-link>
            </li>
            <li class="mb-2">
              <router-link to="/about" class="text-light text-decoration-none">About Us</router-link>
            </li>
            <li class="mb-2">
              <router-link to="/contact" class="text-light text-decoration-none">Contact</router-link>
            </li>
          </ul>
        </div>

        <!-- Categories -->
        <div class="col-lg-2 col-md-6 mb-4">
          <h6 class="text-primary mb-3">Categories</h6>
          <ul class="list-unstyled">
            <li class="mb-2">
              <router-link to="/menu/smoothie" class="text-light text-decoration-none">Smoothies</router-link>
            </li>
            <li class="mb-2">
              <router-link to="/menu/bowl" class="text-light text-decoration-none">Bowls</router-link>
            </li>
            <li class="mb-2">
              <router-link to="/menu/juice" class="text-light text-decoration-none">Juices</router-link>
            </li>
            <li class="mb-2">
              <router-link to="/menu/protein" class="text-light text-decoration-none">Protein</router-link>
            </li>
          </ul>
        </div>

        <!-- Contact Info -->
        <div class="col-lg-4 col-md-6 mb-4">
          <h6 class="text-primary mb-3">Contact Info</h6>
          <div class="contact-info">
            <div class="d-flex align-items-center mb-2">
              <i class="fas fa-map-marker-alt text-primary me-3"></i>
              <span class="text-light">123 Healthy Street, Wellness City, WC 12345</span>
            </div>
            <div class="d-flex align-items-center mb-2">
              <i class="fas fa-phone text-primary me-3"></i>
              <span class="text-light">(555) 123-SMOOTH</span>
            </div>
            <div class="d-flex align-items-center mb-2">
              <i class="fas fa-envelope text-primary me-3"></i>
              <span class="text-light"><EMAIL></span>
            </div>
            <div class="d-flex align-items-center">
              <i class="fas fa-clock text-primary me-3"></i>
              <span class="text-light">Mon-Sun: 7:00 AM - 9:00 PM</span>
            </div>
          </div>
        </div>
      </div>

      <!-- Bottom Section -->
      <hr class="my-4 border-secondary">
      <div class="row align-items-center">
        <div class="col-md-6">
          <p class="mb-0 text-light">
            &copy; {{ currentYear }} Smoothie Vue. All rights reserved.
          </p>
        </div>
        <div class="col-md-6 text-md-end">
          <div class="footer-links">
            <a href="#" class="text-light text-decoration-none me-3">Privacy Policy</a>
            <a href="#" class="text-light text-decoration-none me-3">Terms of Service</a>
            <a href="#" class="text-light text-decoration-none">Support</a>
          </div>
        </div>
      </div>
    </div>
  </footer>
</template>

<script>
export default {
  name: 'FooterComponent',
  
  computed: {
    currentYear() {
      return new Date().getFullYear()
    }
  }
}
</script>

<style scoped>
.footer {
  background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
}

.social-links a {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
}

.social-links a:hover {
  background-color: var(--primary-color);
  transform: translateY(-3px);
  color: white !important;
}

.footer a:hover {
  color: var(--primary-color) !important;
  transition: color 0.3s ease;
}

.contact-info i {
  width: 20px;
  text-align: center;
}

.footer-links a {
  font-size: 0.9rem;
}

.footer-links a:hover {
  text-decoration: underline !important;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .footer .col-md-6.text-md-end {
    text-align: center !important;
    margin-top: 1rem;
  }
  
  .footer-links {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
  }
  
  .social-links {
    text-align: center;
    margin-top: 1rem;
  }
}
</style>
