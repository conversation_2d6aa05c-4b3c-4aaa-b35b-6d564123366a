<template>
  <nav class="navbar navbar-expand-lg navbar-light bg-white fixed-top shadow-sm">
    <div class="container">
      <!-- Brand Logo -->
      <router-link class="navbar-brand d-flex align-items-center" to="/">
        <i class="fas fa-leaf text-primary me-2"></i>
        <span class="fw-bold text-primary">Smoothie Vue</span>
      </router-link>

      <!-- Mobile Menu Toggle -->
      <button 
        class="navbar-toggler border-0" 
        type="button" 
        @click="toggleMobileMenu"
        aria-label="Toggle navigation"
      >
        <span class="navbar-toggler-icon"></span>
      </button>

      <!-- Navigation Menu -->
      <div class="navbar-collapse" :class="{ 'show': isMobileMenuOpen }">
        <ul class="navbar-nav me-auto mb-2 mb-lg-0">
          <li class="nav-item">
            <router-link class="nav-link" to="/" @click="closeMobileMenu">
              <i class="fas fa-home me-1"></i>Home
            </router-link>
          </li>
          <li class="nav-item">
            <router-link class="nav-link" to="/menu" @click="closeMobileMenu">
              <i class="fas fa-utensils me-1"></i>Menu
            </router-link>
          </li>
          <li class="nav-item">
            <router-link class="nav-link" to="/about" @click="closeMobileMenu">
              <i class="fas fa-info-circle me-1"></i>About
            </router-link>
          </li>
          <li class="nav-item">
            <router-link class="nav-link" to="/contact" @click="closeMobileMenu">
              <i class="fas fa-envelope me-1"></i>Contact
            </router-link>
          </li>
        </ul>

        <!-- Right Side Menu -->
        <div class="d-flex align-items-center">
          <!-- Cart Button -->
          <router-link 
            v-if="isAuthenticated" 
            to="/cart" 
            class="btn btn-outline-primary me-2 position-relative"
            @click="closeMobileMenu"
          >
            <i class="fas fa-shopping-cart"></i>
            <span class="d-none d-md-inline ms-1">Cart</span>
            <span 
              v-if="cartItemCount > 0" 
              class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger"
            >
              {{ cartItemCount }}
            </span>
          </router-link>

          <!-- User Menu (Authenticated) -->
          <div v-if="isAuthenticated" class="dropdown">
            <button 
              class="btn btn-outline-secondary dropdown-toggle" 
              type="button" 
              id="userDropdown" 
              data-bs-toggle="dropdown"
            >
              <i class="fas fa-user me-1"></i>
              <span class="d-none d-md-inline">{{ userName }}</span>
            </button>
            <ul class="dropdown-menu dropdown-menu-end">
              <li>
                <router-link class="dropdown-item" to="/profile" @click="closeMobileMenu">
                  <i class="fas fa-user-circle me-2"></i>Profile
                </router-link>
              </li>
              <li>
                <router-link class="dropdown-item" to="/orders" @click="closeMobileMenu">
                  <i class="fas fa-receipt me-2"></i>My Orders
                </router-link>
              </li>
              <li><hr class="dropdown-divider"></li>
              <li>
                <button class="dropdown-item" @click="logout">
                  <i class="fas fa-sign-out-alt me-2"></i>Logout
                </button>
              </li>
            </ul>
          </div>

          <!-- Login/Register (Guest) -->
          <div v-else class="d-flex">
            <router-link 
              to="/login" 
              class="btn btn-outline-primary me-2"
              @click="closeMobileMenu"
            >
              <i class="fas fa-sign-in-alt me-1"></i>Login
            </router-link>
            <router-link 
              to="/register" 
              class="btn btn-primary"
              @click="closeMobileMenu"
            >
              <i class="fas fa-user-plus me-1"></i>Register
            </router-link>
          </div>
        </div>
      </div>
    </div>
  </nav>
</template>

<script>
import { mapState, mapGetters, mapActions } from 'vuex'

export default {
  name: 'NavBar',
  
  computed: {
    ...mapState(['isMobileMenuOpen']),
    ...mapGetters(['isAuthenticated', 'cartItemCount', 'userName'])
  },
  
  methods: {
    ...mapActions(['toggleMobileMenu', 'closeMobileMenu', 'logoutUser']),
    
    async logout() {
      try {
        await this.logoutUser()
        this.$router.push('/')
        this.showSuccess('Logged out successfully')
      } catch (error) {
        this.handleApiError(error, 'Failed to logout')
      }
    }
  }
}
</script>

<style scoped>
.navbar {
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.navbar-brand {
  font-size: 1.5rem;
  text-decoration: none !important;
}

.navbar-brand:hover {
  transform: scale(1.05);
  transition: transform 0.2s ease;
}

.nav-link {
  font-weight: 500;
  transition: all 0.3s ease;
  border-radius: 6px;
  margin: 0 2px;
}

.nav-link:hover {
  background-color: var(--primary-color);
  color: white !important;
  transform: translateY(-1px);
}

.nav-link.router-link-active {
  color: var(--primary-color) !important;
  font-weight: 600;
}

.btn {
  border-radius: 25px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.dropdown-menu {
  border: none;
  box-shadow: 0 8px 25px rgba(0,0,0,0.1);
  border-radius: 12px;
  margin-top: 8px;
}

.dropdown-item {
  padding: 10px 20px;
  transition: all 0.2s ease;
}

.dropdown-item:hover {
  background-color: var(--primary-color);
  color: white;
  transform: translateX(5px);
}

.badge {
  font-size: 0.7rem;
}

/* Mobile Styles */
@media (max-width: 991px) {
  .navbar-collapse {
    background-color: white;
    border-radius: 12px;
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
    margin-top: 15px;
    padding: 20px;
  }
  
  .navbar-nav {
    margin-bottom: 20px;
  }
  
  .nav-link {
    padding: 12px 16px;
    margin: 4px 0;
  }
}
</style>
