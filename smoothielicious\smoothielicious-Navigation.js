import React from 'react';
import { <PERSON> } from 'react-router-dom';
import {
  <PERSON><PERSON><PERSON>,
  Navbar,
  Nav<PERSON><PERSON><PERSON><PERSON>,
  Nav<PERSON><PERSON>rand,
  Nav,
  NavItem,
  NavLink,
  Container
} from 'reactstrap';

class Navigation extends React.Component {
  constructor(props) {
    super(props);

    this.toggle = this.toggle.bind(this);
    this.state = {
      isOpen: false
    };
  }

  toggle() {
    this.setState({
      isOpen: !this.state.isOpen
    });
  }

  render() {
    return (
      <div>
        <Navbar color="light" light expand="md" className="mb-4">
          <Container>
            <NavbarBrand tag={Link} to="/">
              <img 
                src="/logo.png" 
                alt="Smoothielicious Logo" 
                height="40" 
                className="d-inline-block align-top mr-2" 
              />
              Smoothielicious
            </NavbarBrand>
            <NavbarToggler onClick={this.toggle} />
            <Collapse isOpen={this.state.isOpen} navbar>
              <Nav className="ml-auto" navbar>
                <NavItem>
                  <NavLink tag={Link} to="/">Home</NavLink>
                </NavItem>
                <NavItem>
                  <NavLink tag={Link} to="/menu">Menu</NavLink>
                </NavItem>
                <NavItem>
                  <NavLink tag={Link} to="/offers">Offers</NavLink>
                </NavItem>
                <NavItem>
                  <NavLink tag={Link} to="/order">Order Online</NavLink>
                </NavItem>
                <NavItem>
                  <NavLink tag={Link} to="/cart">
                    <i className="fas fa-shopping-cart"></i> Cart
                    {this.props.cartCount > 0 && (
                      <span className="badge badge-pill badge-success ml-1">
                        {this.props.cartCount}
                      </span>
                    )}
                  </NavLink>
                </NavItem>
              </Nav>
            </Collapse>
          </Container>
        </Navbar>
      </div>
    );
  }
}

export default Navigation;
