# Smoothie Vue Backend

A Node.js/Express backend API for the Smoothie Vue restaurant ordering system.

## Features

- RESTful API for food/smoothie management
- User authentication and management
- Shopping cart functionality
- Order processing
- MySQL database integration
- Environment-based configuration
- Error handling and validation

## Tech Stack

- **Runtime**: Node.js
- **Framework**: Express.js
- **Database**: MySQL
- **Environment**: dotenv
- **Development**: Nodemon

## Project Structure

```
backend/
├── config/
│   └── database.js          # Database connection
├── controllers/
│   ├── food.js             # Food/smoothie operations
│   ├── user.js             # User management
│   └── cart.js             # Shopping cart
├── models/
│   ├── FoodModel.js        # Food data layer
│   ├── UserModel.js        # User data layer
│   └── CartModel.js        # Cart data layer
├── routes/
│   └── routes.js           # API routes
├── database/
│   └── schema.sql          # Database schema
├── .env.example            # Environment template
├── package.json
└── index.js               # Main server file
```

## Setup Instructions

### 1. Install Dependencies
```bash
cd backend
npm install
```

### 2. Database Setup
1. Create MySQL database:
```sql
CREATE DATABASE db_smoothie_vue;
```

2. Import the schema:
```bash
mysql -u root -p db_smoothie_vue < database/schema.sql
```

### 3. Environment Configuration
1. Copy the environment template:
```bash
cp .env.example .env
```

2. Update `.env` with your database credentials:
```env
DB_HOST=localhost
DB_USER=root
DB_PASSWORD=your_password
DB_NAME=db_smoothie_vue
PORT=8001
NODE_ENV=development
```

### 4. Start the Server

**Development mode:**
```bash
npm run dev
```

**Production mode:**
```bash
npm start
```

## API Endpoints

### Food/Smoothies
- `GET /api/foods` - Get all food items
- `GET /api/foods/:id` - Get food by ID
- `GET /api/foods/category/:category` - Get foods by category
- `GET /api/foods/search?q=term` - Search foods
- `POST /api/foods` - Create new food (admin)
- `PUT /api/foods/:id` - Update food (admin)
- `DELETE /api/foods/:id` - Delete food (admin)

### Users
- `GET /api/users` - Get all users (admin)
- `GET /api/users/:id` - Get user by ID
- `GET /api/users/email/:email` - Get user by email
- `POST /api/users` - Create new user
- `POST /api/users/login` - Login user
- `PUT /api/users/:id` - Update user
- `DELETE /api/users/:id` - Delete user

### Cart
- `GET /api/cart/:id` - Get user's cart items
- `GET /api/cart/:id/summary` - Get cart summary
- `GET /api/cart/:user_id/:food_id` - Get specific cart item
- `POST /api/cart` - Add item to cart
- `PUT /api/cart` - Update cart item quantity
- `DELETE /api/cart/:user_id/:food_id` - Remove item from cart
- `DELETE /api/cart/:id` - Clear user's cart

### Utility
- `GET /api/health` - Health check
- `GET /api/categories` - Get available categories
- `GET /api/stats/menu` - Get menu statistics (admin)

## Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `DB_HOST` | Database host | localhost |
| `DB_USER` | Database user | root |
| `DB_PASSWORD` | Database password | (empty) |
| `DB_NAME` | Database name | db_smoothie_vue |
| `PORT` | Server port | 8001 |
| `NODE_ENV` | Environment | development |
| `FRONTEND_URL` | Frontend URL for CORS | http://localhost:8080 |

## Development

### Adding New Features
1. Create model in `models/` directory
2. Create controller in `controllers/` directory
3. Add routes in `routes/routes.js`
4. Update database schema if needed

### Database Migrations
When updating the database schema:
1. Update `database/schema.sql`
2. Create migration scripts for existing databases
3. Document changes in this README

## Deployment

### Production Checklist
- [ ] Set `NODE_ENV=production`
- [ ] Configure production database
- [ ] Set secure JWT secret
- [ ] Configure CORS for production frontend URL
- [ ] Set up SSL/HTTPS
- [ ] Configure logging
- [ ] Set up monitoring

### Docker Deployment
```dockerfile
FROM node:16-alpine
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production
COPY . .
EXPOSE 8001
CMD ["npm", "start"]
```

## Contributing

1. Follow the existing code structure
2. Add proper error handling
3. Include input validation
4. Write clear commit messages
5. Test all endpoints before submitting

## License

MIT License
