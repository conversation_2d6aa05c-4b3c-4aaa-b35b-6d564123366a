<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width,initial-scale=1.0">
    <link rel="icon" href="<%= BASE_URL %>favicon.ico">
    
    <!-- SEO Meta Tags -->
    <title>Smoothie Vue - Fresh & Delicious Smoothies</title>
    <meta name="description" content="Order fresh, healthy smoothies, bowls, and juices online. Made with premium ingredients and delivered fresh to your door.">
    <meta name="keywords" content="smoothies, healthy drinks, fresh juice, smoothie bowls, protein shakes, organic">
    <meta name="author" content="Smoothie Vue">
    
    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="Smoothie Vue - Fresh & Delicious Smoothies">
    <meta property="og:description" content="Order fresh, healthy smoothies, bowls, and juices online.">
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://smoothievue.com">
    <meta property="og:image" content="<%= BASE_URL %>images/og-image.jpg">
    
    <!-- Twitter Card Meta Tags -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="Smoothie Vue - Fresh & Delicious Smoothies">
    <meta name="twitter:description" content="Order fresh, healthy smoothies, bowls, and juices online.">
    <meta name="twitter:image" content="<%= BASE_URL %>images/twitter-card.jpg">
    
    <!-- Theme Color -->
    <meta name="theme-color" content="#27ae60">
    <meta name="msapplication-TileColor" content="#27ae60">
    
    <!-- Apple Touch Icon -->
    <link rel="apple-touch-icon" href="<%= BASE_URL %>apple-touch-icon.png">
    
    <!-- Manifest -->
    <link rel="manifest" href="<%= BASE_URL %>manifest.json">
    
    <!-- Preconnect to external domains -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Bootstrap CSS (if not using Vue Bootstrap) -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Custom CSS Variables -->
    <style>
      :root {
        --primary-color: #27ae60;
        --secondary-color: #2ecc71;
        --accent-color: #f39c12;
        --text-dark: #2c3e50;
        --text-light: #7f8c8d;
        --background-light: #f8f9fa;
        --white: #ffffff;
        --shadow: 0 2px 10px rgba(0,0,0,0.1);
        --border-radius: 8px;
        --transition: all 0.3s ease;
      }
      
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }
      
      body {
        font-family: 'Poppins', sans-serif;
        line-height: 1.6;
        color: var(--text-dark);
        background-color: var(--background-light);
      }
      
      #app {
        min-height: 100vh;
        display: flex;
        flex-direction: column;
      }
      
      /* Loading spinner */
      .loading-spinner {
        display: flex;
        justify-content: center;
        align-items: center;
        height: 100vh;
        flex-direction: column;
      }
      
      .spinner {
        width: 50px;
        height: 50px;
        border: 4px solid var(--background-light);
        border-top: 4px solid var(--primary-color);
        border-radius: 50%;
        animation: spin 1s linear infinite;
      }
      
      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }
      
      .loading-text {
        margin-top: 20px;
        color: var(--text-light);
        font-weight: 500;
      }
    </style>
  </head>
  <body>
    <noscript>
      <strong>We're sorry but Smoothie Vue doesn't work properly without JavaScript enabled. Please enable it to continue.</strong>
    </noscript>
    
    <!-- App container -->
    <div id="app">
      <!-- Loading fallback -->
      <div class="loading-spinner">
        <div class="spinner"></div>
        <div class="loading-text">Loading Smoothie Vue...</div>
      </div>
    </div>
    
    <!-- Built files will be auto injected -->
  </body>
</html>
