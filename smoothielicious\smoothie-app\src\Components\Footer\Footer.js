import React from 'react';
import { Container, <PERSON>, Col } from 'reactstrap';
import { Link } from 'react-router-dom';

const Footer = () => {
  return (
    <footer className="mt-5 py-5">
      <Container>
        <Row>
          <Col md={4} className="footer-links">
            <h5>Smoothielicious</h5>
            <p>
              We're passionate about creating delicious and nutritious smoothies
              using only the freshest ingredients. Our mission is to help you
              live a healthier lifestyle one smoothie at a time.
            </p>
            <div className="social-icons">
              <a href="https://facebook.com" target="_blank" rel="noopener noreferrer">
                <i className="fab fa-facebook-f"></i>
              </a>
              <a href="https://instagram.com" target="_blank" rel="noopener noreferrer">
                <i className="fab fa-instagram"></i>
              </a>
              <a href="https://twitter.com" target="_blank" rel="noopener noreferrer">
                <i className="fab fa-twitter"></i>
              </a>
            </div>
          </Col>
          <Col md={4} className="footer-links">
            <h5>Quick Links</h5>
            <ul>
              <li>
                <Link to="/">Home</Link>
              </li>
              <li>
                <Link to="/menu">Menu</Link>
              </li>
              <li>
                <Link to="/offers">Special Offers</Link>
              </li>
              <li>
                <Link to="/order">Order Online</Link>
              </li>
              <li>
                <Link to="/cart">Cart</Link>
              </li>
            </ul>
          </Col>
          <Col md={4} className="footer-links">
            <h5>Contact Us</h5>
            <ul>
              <li>
                <i className="fas fa-map-marker-alt mr-2"></i> 123 Smoothie Lane, Fruitville, FL 12345
              </li>
              <li>
                <i className="fas fa-phone mr-2"></i> (*************
              </li>
              <li>
                <i className="fas fa-envelope mr-2"></i> <EMAIL>
              </li>
              <li>
                <i className="fas fa-clock mr-2"></i> Mon-Fri: 8am-8pm, Sat-Sun: 9am-7pm
              </li>
            </ul>
          </Col>
        </Row>
        <Row className="mt-4">
          <Col className="text-center">
            <p className="mb-0">
              &copy; {new Date().getFullYear()} Smoothielicious. All rights reserved.
            </p>
          </Col>
        </Row>
      </Container>
    </footer>
  );
};

export default Footer;
