import React, { Component } from 'react';
import { Container, Row, Col, Button, Card, CardBody, CardTitle, CardText, Table } from 'reactstrap';
import { Link } from 'react-router-dom';
import Navigation from '../../Components/Navigation/Navigation';
import Footer from '../../Components/Footer/Footer';

class Cart extends Component {
  calculateTotal = () => {
    let total = 0;
    this.props.data.forEach(item => {
      total += item.price * item.counter;
    });
    return total.toFixed(2);
  }

  render() {
    const { data, adding, remove } = this.props;
    
    return (
      <div>
        <Navigation cartCount={data.length} />
        
        <Container className="cart-section">
          <h1 className="text-center mb-5">Your Cart</h1>
          
          {data.length === 0 ? (
            <div className="text-center py-5">
              <h3>Your cart is empty</h3>
              <p className="lead mb-4">Add some delicious smoothies to get started!</p>
              <Button 
                color="success" 
                size="lg" 
                tag={Link} 
                to="/menu"
              >
                Browse Menu
              </Button>
            </div>
          ) : (
            <Row>
              <Col md={8}>
                <Card>
                  <CardBody>
                    <CardTitle tag="h3">Cart Items</CardTitle>
                    <Table responsive>
                      <thead>
                        <tr>
                          <th>Item</th>
                          <th>Price</th>
                          <th>Quantity</th>
                          <th>Total</th>
                          <th>Actions</th>
                        </tr>
                      </thead>
                      <tbody>
                        {data.map((item, index) => (
                          <tr key={index}>
                            <td>
                              <div className="d-flex align-items-center">
                                {item.image && (
                                  <img 
                                    src={item.image} 
                                    alt={item.head} 
                                    style={{ width: '50px', height: '50px', objectFit: 'cover', marginRight: '10px' }}
                                  />
                                )}
                                <div>
                                  <h5 className="mb-0">{item.head}</h5>
                                  <small className="text-muted">{item.desc}</small>
                                </div>
                              </div>
                            </td>
                            <td>${item.price.toFixed(2)}</td>
                            <td>{item.counter}</td>
                            <td>${(item.price * item.counter).toFixed(2)}</td>
                            <td>
                              <Button 
                                color="success" 
                                size="sm" 
                                className="mr-2"
                                onClick={() => adding()({
                                  head: item.head,
                                  desc: item.desc,
                                  price: item.price,
                                  counter: 1,
                                  image: item.image
                                })}
                              >
                                <i className="fas fa-plus"></i>
                              </Button>
                              <Button 
                                color="danger" 
                                size="sm"
                                onClick={() => remove()({
                                  head: item.head
                                })}
                              >
                                <i className="fas fa-minus"></i>
                              </Button>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </Table>
                  </CardBody>
                </Card>
              </Col>
              
              <Col md={4}>
                <Card>
                  <CardBody>
                    <CardTitle tag="h3">Order Summary</CardTitle>
                    <Table>
                      <tbody>
                        <tr>
                          <td>Subtotal:</td>
                          <td className="text-right">${this.calculateTotal()}</td>
                        </tr>
                        <tr>
                          <td>Tax:</td>
                          <td className="text-right">${(this.calculateTotal() * 0.08).toFixed(2)}</td>
                        </tr>
                        <tr>
                          <th>Total:</th>
                          <th className="text-right">${(parseFloat(this.calculateTotal()) + parseFloat(this.calculateTotal()) * 0.08).toFixed(2)}</th>
                        </tr>
                      </tbody>
                    </Table>
                    <Button 
                      color="success" 
                      block 
                      size="lg" 
                      tag={Link} 
                      to="/order"
                    >
                      Proceed to Checkout
                    </Button>
                    <Button 
                      color="link" 
                      block 
                      tag={Link} 
                      to="/menu"
                    >
                      Continue Shopping
                    </Button>
                  </CardBody>
                </Card>
              </Col>
            </Row>
          )}
        </Container>
        
        <Footer />
      </div>
    );
  }
}

export default Cart;
