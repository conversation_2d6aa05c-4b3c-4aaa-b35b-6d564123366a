<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Smoothielicious - Fresh & Healthy Smoothies</title>
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://use.fontawesome.com/releases/v5.4.1/css/all.css">
    <link href="https://fonts.googleapis.com/css?family=Montserrat|Open+Sans|Roboto" rel="stylesheet">
    <style>
        :root {
            --primary-color: #4CAF50;  /* Green */
            --secondary-color: #FF9800;  /* Orange */
            --accent-color: #E91E63;  /* Pink */
            --light-color: #F5F5F5;
            --dark-color: #333333;
            --text-color: #212121;
            --text-light: #757575;
        }

        body {
            font-family: 'Roboto', 'Open Sans', sans-serif;
            color: var(--text-color);
            background-color: var(--light-color);
            margin: 0;
            padding: 0;
        }

        .navbar {
            background-color: var(--primary-color) !important;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .navbar-brand {
            color: white !important;
            font-weight: bold;
            font-size: 1.5rem;
        }

        .nav-link {
            color: white !important;
            font-weight: 500;
            margin: 0 10px;
            transition: all 0.3s ease;
        }

        .nav-link:hover {
            color: var(--secondary-color) !important;
            transform: translateY(-2px);
        }

        .hero {
            background-image: linear-gradient(rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0.5)), url('https://images.unsplash.com/photo-1589734580748-6d9421464885?ixlib=rb-1.2.1&auto=format&fit=crop&w=1350&q=80');
            background-size: cover;
            background-position: center;
            color: white;
            padding: 100px 0;
            text-align: center;
        }

        .hero h1 {
            font-size: 3rem;
            margin-bottom: 20px;
        }

        .hero p {
            font-size: 1.2rem;
            max-width: 600px;
            margin: 0 auto 30px;
        }

        .menu-section {
            padding: 60px 0;
        }

        .menu-item {
            margin-bottom: 30px;
            transition: all 0.3s ease;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }

        .menu-item:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
        }

        .menu-item img {
            width: 100%;
            height: 200px;
            object-fit: cover;
        }

        .menu-item-content {
            padding: 20px;
        }

        .menu-item h3 {
            color: var(--primary-color);
            margin-bottom: 10px;
        }

        .menu-item p {
            color: var(--text-light);
            margin-bottom: 15px;
        }

        .menu-item .price {
            font-weight: bold;
            color: var(--accent-color);
            font-size: 1.2rem;
        }

        .btn-success {
            background-color: var(--primary-color) !important;
            border-color: var(--primary-color) !important;
        }

        .btn-success:hover {
            background-color: #3d8b40 !important;
            border-color: #3d8b40 !important;
        }

        .btn-warning {
            background-color: var(--secondary-color) !important;
            border-color: var(--secondary-color) !important;
            color: white !important;
        }

        .btn-warning:hover {
            background-color: #e68a00 !important;
            border-color: #e68a00 !important;
        }

        footer {
            background-color: var(--dark-color);
            color: white;
            padding: 40px 0;
            margin-top: 60px;
        }

        .footer-links h5 {
            color: var(--secondary-color);
            margin-bottom: 20px;
        }

        .footer-links ul {
            list-style: none;
            padding: 0;
        }

        .footer-links li {
            margin-bottom: 10px;
        }

        .footer-links a {
            color: white;
            text-decoration: none;
            transition: all 0.3s ease;
        }

        .footer-links a:hover {
            color: var(--secondary-color);
        }

        .social-icons {
            display: flex;
            margin-top: 20px;
        }

        .social-icons a {
            color: white;
            margin-right: 15px;
            font-size: 1.5rem;
            transition: all 0.3s ease;
        }

        .social-icons a:hover {
            color: var(--secondary-color);
            transform: translateY(-3px);
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container">
            <a class="navbar-brand" href="#">
                <img src="https://via.placeholder.com/40x40" alt="Smoothielicious Logo" height="40" class="d-inline-block align-top mr-2">
                Smoothielicious
            </a>
            <button class="navbar-toggler" type="button" data-toggle="collapse" data-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ml-auto">
                    <li class="nav-item active">
                        <a class="nav-link" href="#">Home</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#menu">Menu</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#offers">Offers</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#contact">Contact</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="cart.html">
                            <i class="fas fa-shopping-cart"></i> Cart
                            <span class="badge badge-pill badge-success ml-1" id="cartCount">0</span>
                        </a>
                    </li>
                    <li class="nav-item ml-2">
                        <a class="btn btn-success" href="#login">
                            <i class="fas fa-sign-in-alt mr-1"></i> Login
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <div class="hero">
        <div class="container">
            <h1>Welcome to Smoothielicious</h1>
            <p>Fresh & Healthy Smoothies</p>
            <p>We use only the freshest ingredients to create delicious and nutritious smoothies that will energize your day.</p>
            <a href="#menu" class="btn btn-success btn-lg mr-3">View Our Menu</a>
            <a href="#contact" class="btn btn-warning btn-lg">Order Online</a>
        </div>
    </div>

    <!-- Features Section -->
    <div class="container py-5">
        <h2 class="text-center mb-5">Why Choose Smoothielicious?</h2>
        <div class="row">
            <div class="col-md-4 mb-4">
                <div class="card h-100 text-center">
                    <div class="card-body">
                        <div class="mb-3">
                            <i class="fas fa-leaf fa-3x text-success"></i>
                        </div>
                        <h4 class="card-title">Fresh Ingredients</h4>
                        <p class="card-text">We use only the freshest fruits and vegetables in our smoothies.</p>
                    </div>
                </div>
            </div>
            <div class="col-md-4 mb-4">
                <div class="card h-100 text-center">
                    <div class="card-body">
                        <div class="mb-3">
                            <i class="fas fa-heart fa-3x text-success"></i>
                        </div>
                        <h4 class="card-title">Nutritious Options</h4>
                        <p class="card-text">Our smoothies are packed with vitamins and nutrients.</p>
                    </div>
                </div>
            </div>
            <div class="col-md-4 mb-4">
                <div class="card h-100 text-center">
                    <div class="card-body">
                        <div class="mb-3">
                            <i class="fas fa-blender fa-3x text-success"></i>
                        </div>
                        <h4 class="card-title">Custom Blends</h4>
                        <p class="card-text">Create your own custom smoothie with your favorite ingredients.</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Menu Section -->
    <div id="menu" class="menu-section">
        <div class="container">
            <h2 class="text-center mb-5">Our Menu</h2>
            <div class="row">
                <div class="col-md-4 mb-4">
                    <div class="menu-item" style="cursor: pointer;">
                        <img src="https://images.unsplash.com/photo-1553530666-ba11a90bb0ae?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=80" alt="Berry Blast">
                        <div class="menu-item-content">
                            <h3>Berry Blast</h3>
                            <p>A delicious blend of strawberries, blueberries, and raspberries</p>
                            <div class="d-flex justify-content-between align-items-center">
                                <span class="price">$5.99</span>
                                <button class="btn btn-success quick-view-btn" data-toggle="modal" data-target="#quickViewModal">Quick View</button>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-4 mb-4">
                    <div class="menu-item" style="cursor: pointer;">
                        <img src="https://images.unsplash.com/photo-1589733955941-5eeaf752f6dd?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=80" alt="Tropical Paradise">
                        <div class="menu-item-content">
                            <h3>Tropical Paradise</h3>
                            <p>Mango, pineapple, and banana with coconut milk</p>
                            <div class="d-flex justify-content-between align-items-center">
                                <span class="price">$6.49</span>
                                <button class="btn btn-success quick-view-btn" data-toggle="modal" data-target="#quickViewModal">Quick View</button>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-4 mb-4">
                    <div class="menu-item" style="cursor: pointer;">
                        <img src="https://images.unsplash.com/photo-1590301157890-4810ed352733?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=80" alt="Green Machine">
                        <div class="menu-item-content">
                            <h3>Green Machine</h3>
                            <p>Spinach, kale, apple, and banana with almond milk</p>
                            <div class="d-flex justify-content-between align-items-center">
                                <span class="price">$6.99</span>
                                <button class="btn btn-success quick-view-btn" data-toggle="modal" data-target="#quickViewModal">Quick View</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer id="contact" class="bg-dark text-white py-5">
        <div class="container">
            <div class="row">
                <div class="col-md-4 mb-4 mb-md-0">
                    <h5 class="text-uppercase mb-4">Smoothielicious</h5>
                    <p>
                        We're passionate about creating delicious and nutritious smoothies
                        using only the freshest ingredients. Our mission is to help you
                        live a healthier lifestyle one smoothie at a time.
                    </p>
                    <div class="social-icons">
                        <a href="https://facebook.com" target="_blank" rel="noopener noreferrer" class="text-white mr-3">
                            <i class="fab fa-facebook-f fa-lg"></i>
                        </a>
                        <a href="https://instagram.com" target="_blank" rel="noopener noreferrer" class="text-white mr-3">
                            <i class="fab fa-instagram fa-lg"></i>
                        </a>
                        <a href="https://twitter.com" target="_blank" rel="noopener noreferrer" class="text-white mr-3">
                            <i class="fab fa-twitter fa-lg"></i>
                        </a>
                    </div>
                </div>

                <div class="col-md-2 mb-4 mb-md-0">
                    <h5 class="text-uppercase mb-4">Quick Links</h5>
                    <ul class="list-unstyled">
                        <li class="mb-2">
                            <a href="#" class="text-white">Home</a>
                        </li>
                        <li class="mb-2">
                            <a href="#menu" class="text-white">Menu</a>
                        </li>
                        <li class="mb-2">
                            <a href="#offers" class="text-white">Special Offers</a>
                        </li>
                        <li class="mb-2">
                            <a href="#contact" class="text-white">Contact Us</a>
                        </li>
                        <li class="mb-2">
                            <a href="#cart" class="text-white">Cart</a>
                        </li>
                    </ul>
                </div>

                <div class="col-md-3 mb-4 mb-md-0">
                    <h5 class="text-uppercase mb-4">Contact Us</h5>
                    <ul class="list-unstyled">
                        <li class="mb-2">
                            <i class="fas fa-map-marker-alt mr-2"></i> 123 Smoothie Lane, Fruitville, FL 12345
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-phone mr-2"></i> (*************
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-envelope mr-2"></i> <EMAIL>
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-clock mr-2"></i> Mon-Fri: 8am-8pm, Sat-Sun: 9am-7pm
                        </li>
                    </ul>
                </div>

                <div class="col-md-3">
                    <h5 class="text-uppercase mb-4">Newsletter</h5>
                    <p>Subscribe to our newsletter for updates and special offers.</p>
                    <form>
                        <div class="form-group">
                            <input type="email" class="form-control mb-2" placeholder="Your email address">
                            <button type="submit" class="btn btn-success btn-block">Subscribe</button>
                        </div>
                    </form>
                </div>
            </div>

            <hr class="my-4 bg-light">

            <div class="row">
                <div class="col text-center">
                    <p class="mb-0">
                        &copy; <script>document.write(new Date().getFullYear())</script> Smoothielicious. All rights reserved.
                    </p>
                </div>
            </div>
        </div>
    </footer>

    <!-- Quick View Modal -->
    <div class="modal fade" id="quickViewModal" tabindex="-1" role="dialog" aria-labelledby="quickViewModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="quickViewModalLabel">Item Details</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <img id="modalItemImage" src="" alt="Item Image" class="img-fluid rounded mb-3" style="max-height: 300px; width: 100%; object-fit: cover;">
                        </div>
                        <div class="col-md-6">
                            <h4 id="modalItemName"></h4>
                            <p id="modalItemDescription" class="text-muted"></p>

                            <div class="mb-3">
                                <span id="modalItemPrice" class="h5 text-success"></span>
                            </div>

                            <div class="form-group">
                                <label for="modalQuantity">Quantity</label>
                                <input type="number" class="form-control" id="modalQuantity" value="1" min="1" max="10">
                            </div>

                            <div class="form-group">
                                <label for="modalSpecialInstructions">Special Instructions</label>
                                <textarea class="form-control" id="modalSpecialInstructions" rows="3" placeholder="Any special requests?"></textarea>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <div class="mr-auto">
                        <h5>Total: <span id="modalTotalPrice"></span></h5>
                    </div>
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-success" id="addToCartBtn">
                        <i class="fas fa-cart-plus mr-1"></i> Add to Cart
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://code.jquery.com/jquery-3.6.4.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/popper.js@1.16.1/dist/umd/popper.min.js"></script>
    <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.min.js"></script>

    <script>
        // Wait for the document to be fully loaded
        $(document).ready(function() {
            // Load cart count from localStorage
            function loadCartCount() {
                let cartItems = JSON.parse(localStorage.getItem('smoothieCart')) || [];
                let cartCount = 0;

                cartItems.forEach(item => {
                    cartCount += item.quantity;
                });

                $('#cartCount').text(cartCount);
            }

            // Load cart count on page load
            loadCartCount();

            // Quick View Modal Functionality
            $('.menu-item').on('click', function(e) {
                if (!$(e.target).hasClass('quick-view-btn')) {
                    const name = $(this).find('h3').text();
                    const description = $(this).find('p').first().text();
                    const price = $(this).find('.price').text().replace('$', '');
                    const image = $(this).find('img').attr('src');

                    $('#modalItemName').text(name);
                    $('#modalItemDescription').text(description);
                    $('#modalItemPrice').text('$' + price);
                    $('#modalItemImage').attr('src', image);
                    $('#modalTotalPrice').text('$' + price);
                    $('#modalQuantity').val(1);
                    $('#modalSpecialInstructions').val('');

                    $('#quickViewModal').modal('show');
                }
            });

            // Quick View Button Click
            $(document).on('click', '.quick-view-btn', function(e) {
                e.stopPropagation();
                const menuItem = $(this).closest('.menu-item');
                const name = menuItem.find('h3').text();
                const description = menuItem.find('p').first().text();
                const price = menuItem.find('.price').text().replace('$', '');
                const image = menuItem.find('img').attr('src');

                $('#modalItemName').text(name);
                $('#modalItemDescription').text(description);
                $('#modalItemPrice').text('$' + price);
                $('#modalItemImage').attr('src', image);
                $('#modalTotalPrice').text('$' + price);
                $('#modalQuantity').val(1);
                $('#modalSpecialInstructions').val('');
            });

            // Update total price when quantity changes
            $(document).on('change', '#modalQuantity', function() {
                const price = parseFloat($('#modalItemPrice').text().replace('$', ''));
                const quantity = parseInt($(this).val());
                const total = (price * quantity).toFixed(2);
                $('#modalTotalPrice').text('$' + total);
            });

            // Add to Cart functionality
            $(document).on('click', '#addToCartBtn', function() {
                const name = $('#modalItemName').text();
                const price = parseFloat($('#modalItemPrice').text().replace('$', ''));
                const quantity = parseInt($('#modalQuantity').val());
                const instructions = $('#modalSpecialInstructions').val();
                const image = $('#modalItemImage').attr('src');
                const description = $('#modalItemDescription').text();

                // Get existing cart items from localStorage
                let cartItems = JSON.parse(localStorage.getItem('smoothieCart')) || [];

                // Check if item already exists in cart
                let itemExists = false;
                for (let i = 0; i < cartItems.length; i++) {
                    if (cartItems[i].name === name) {
                        cartItems[i].quantity += quantity;
                        itemExists = true;
                        break;
                    }
                }

                // If item doesn't exist, add it to cart
                if (!itemExists) {
                    cartItems.push({
                        name: name,
                        price: price,
                        quantity: quantity,
                        instructions: instructions,
                        image: image,
                        description: description
                    });
                }

                // Save cart items to localStorage
                localStorage.setItem('smoothieCart', JSON.stringify(cartItems));

                // Update cart count
                let cartCount = 0;
                cartItems.forEach(item => {
                    cartCount += item.quantity;
                });
                $('#cartCount').text(cartCount);

                // Show success message
                alert(quantity + 'x ' + name + ' added to cart!');

                // Close modal
                $('#quickViewModal').modal('hide');
            });

            // Cart icon click
            $(document).on('click', '.fa-shopping-cart', function(e) {
                let cartCount = parseInt($('#cartCount').text()) || 0;
                if (cartCount === 0) {
                    e.preventDefault();
                    alert('Your cart is empty. Please add items to your cart first.');
                }
            });
        });
    </script>
</body>
</html>
