const { defineConfig } = require('@vue/cli-service')
const path = require('path')

module.exports = defineConfig({
  transpileDependencies: true,
  
  // Output directory for production builds
  outputDir: path.resolve(__dirname, '../backend/dist'),
  
  // Development server configuration
  devServer: {
    port: 8080,
    proxy: {
      '/api': {
        target: process.env.VUE_APP_API_URL || 'http://localhost:8001',
        changeOrigin: true,
        secure: false
      }
    },
    historyApiFallback: true
  },
  
  // Public path for assets
  publicPath: process.env.NODE_ENV === 'production' ? '/' : '/',
  
  // Configure webpack
  configureWebpack: {
    resolve: {
      alias: {
        '@': path.resolve(__dirname, 'src')
      }
    }
  },
  
  // CSS configuration
  css: {
    loaderOptions: {
      sass: {
        additionalData: `@import "@/assets/scss/variables.scss";`
      }
    }
  },
  
  // PWA configuration (optional)
  pwa: {
    name: 'Smoothie Vue',
    themeColor: '#27ae60',
    msTileColor: '#27ae60',
    appleMobileWebAppCapable: 'yes',
    appleMobileWebAppStatusBarStyle: 'black',
    workboxOptions: {
      skipWaiting: true
    }
  }
})
