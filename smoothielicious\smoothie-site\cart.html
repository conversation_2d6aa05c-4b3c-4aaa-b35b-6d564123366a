<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cart - Smoothielicious</title>
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://use.fontawesome.com/releases/v5.4.1/css/all.css">
    <link href="https://fonts.googleapis.com/css?family=Montserrat|Open+Sans|Roboto" rel="stylesheet">
    <style>
        :root {
            --primary-color: #4CAF50;  /* Green */
            --secondary-color: #FF9800;  /* Orange */
            --accent-color: #E91E63;  /* Pink */
            --light-color: #F5F5F5;
            --dark-color: #333333;
            --text-color: #212121;
            --text-light: #757575;
        }

        body {
            font-family: 'Roboto', 'Open Sans', sans-serif;
            color: var(--text-color);
            background-color: var(--light-color);
            margin: 0;
            padding: 0;
        }

        .navbar {
            background-color: var(--primary-color) !important;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .navbar-brand {
            color: white !important;
            font-weight: bold;
            font-size: 1.5rem;
        }

        .nav-link {
            color: white !important;
            font-weight: 500;
            margin: 0 10px;
            transition: all 0.3s ease;
        }

        .nav-link:hover {
            color: var(--secondary-color) !important;
            transform: translateY(-2px);
        }

        .btn-success {
            background-color: var(--primary-color) !important;
            border-color: var(--primary-color) !important;
        }

        .btn-success:hover {
            background-color: #3d8b40 !important;
            border-color: #3d8b40 !important;
        }

        .btn-warning {
            background-color: var(--secondary-color) !important;
            border-color: var(--secondary-color) !important;
            color: white !important;
        }

        .btn-warning:hover {
            background-color: #e68a00 !important;
            border-color: #e68a00 !important;
        }

        footer {
            background-color: var(--dark-color);
            color: white;
            padding: 40px 0;
            margin-top: 60px;
        }

        .cart-item {
            margin-bottom: 20px;
            border-bottom: 1px solid #ddd;
            padding-bottom: 20px;
        }

        .cart-item img {
            max-width: 100px;
            height: auto;
            border-radius: 5px;
        }

        .cart-summary {
            background-color: white;
            border-radius: 5px;
            padding: 20px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .empty-cart {
            text-align: center;
            padding: 50px 0;
        }

        .empty-cart i {
            font-size: 5rem;
            color: #ddd;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container">
            <a class="navbar-brand" href="index.html">
                <img src="https://via.placeholder.com/40x40" alt="Smoothielicious Logo" height="40" class="d-inline-block align-top mr-2">
                Smoothielicious
            </a>
            <button class="navbar-toggler" type="button" data-toggle="collapse" data-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ml-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="index.html">Home</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="index.html#menu">Menu</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="index.html#offers">Offers</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="index.html#contact">Contact</a>
                    </li>
                    <li class="nav-item active">
                        <a class="nav-link" href="cart.html">
                            <i class="fas fa-shopping-cart"></i> Cart
                            <span class="badge badge-pill badge-success ml-1" id="cartCount">0</span>
                        </a>
                    </li>
                    <li class="nav-item ml-2">
                        <a class="btn btn-success" href="#login">
                            <i class="fas fa-sign-in-alt mr-1"></i> Login
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Cart Section -->
    <div class="container py-5">
        <h1 class="mb-4">Your Cart</h1>
        
        <div id="cartContent">
            <!-- Cart items will be displayed here -->
        </div>
    </div>

    <!-- Footer -->
    <footer class="bg-dark text-white py-5">
        <div class="container">
            <div class="row">
                <div class="col-md-4 mb-4 mb-md-0">
                    <h5 class="text-uppercase mb-4">Smoothielicious</h5>
                    <p>
                        We're passionate about creating delicious and nutritious smoothies
                        using only the freshest ingredients. Our mission is to help you
                        live a healthier lifestyle one smoothie at a time.
                    </p>
                    <div class="social-icons">
                        <a href="https://facebook.com" target="_blank" rel="noopener noreferrer" class="text-white mr-3">
                            <i class="fab fa-facebook-f fa-lg"></i>
                        </a>
                        <a href="https://instagram.com" target="_blank" rel="noopener noreferrer" class="text-white mr-3">
                            <i class="fab fa-instagram fa-lg"></i>
                        </a>
                        <a href="https://twitter.com" target="_blank" rel="noopener noreferrer" class="text-white mr-3">
                            <i class="fab fa-twitter fa-lg"></i>
                        </a>
                    </div>
                </div>

                <div class="col-md-2 mb-4 mb-md-0">
                    <h5 class="text-uppercase mb-4">Quick Links</h5>
                    <ul class="list-unstyled">
                        <li class="mb-2">
                            <a href="index.html" class="text-white">Home</a>
                        </li>
                        <li class="mb-2">
                            <a href="index.html#menu" class="text-white">Menu</a>
                        </li>
                        <li class="mb-2">
                            <a href="index.html#offers" class="text-white">Special Offers</a>
                        </li>
                        <li class="mb-2">
                            <a href="index.html#contact" class="text-white">Contact Us</a>
                        </li>
                        <li class="mb-2">
                            <a href="cart.html" class="text-white">Cart</a>
                        </li>
                    </ul>
                </div>

                <div class="col-md-3 mb-4 mb-md-0">
                    <h5 class="text-uppercase mb-4">Contact Us</h5>
                    <ul class="list-unstyled">
                        <li class="mb-2">
                            <i class="fas fa-map-marker-alt mr-2"></i> 123 Smoothie Lane, Fruitville, FL 12345
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-phone mr-2"></i> (*************
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-envelope mr-2"></i> <EMAIL>
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-clock mr-2"></i> Mon-Fri: 8am-8pm, Sat-Sun: 9am-7pm
                        </li>
                    </ul>
                </div>

                <div class="col-md-3">
                    <h5 class="text-uppercase mb-4">Newsletter</h5>
                    <p>Subscribe to our newsletter for updates and special offers.</p>
                    <form>
                        <div class="form-group">
                            <input type="email" class="form-control mb-2" placeholder="Your email address">
                            <button type="submit" class="btn btn-success btn-block">Subscribe</button>
                        </div>
                    </form>
                </div>
            </div>

            <hr class="my-4 bg-light">

            <div class="row">
                <div class="col text-center">
                    <p class="mb-0">
                        &copy; <script>document.write(new Date().getFullYear())</script> Smoothielicious. All rights reserved.
                    </p>
                </div>
            </div>
        </div>
    </footer>

    <script src="https://code.jquery.com/jquery-3.6.4.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/popper.js@1.16.1/dist/umd/popper.min.js"></script>
    <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.min.js"></script>

    <script>
        $(document).ready(function() {
            // Load cart items from localStorage
            function loadCart() {
                let cartItems = JSON.parse(localStorage.getItem('smoothieCart')) || [];
                let cartCount = 0;
                
                // Calculate total cart count
                cartItems.forEach(item => {
                    cartCount += item.quantity;
                });
                
                // Update cart count badge
                $('#cartCount').text(cartCount);
                
                // Display cart items or empty cart message
                if (cartItems.length === 0) {
                    $('#cartContent').html(`
                        <div class="empty-cart">
                            <i class="fas fa-shopping-cart"></i>
                            <h3>Your cart is empty</h3>
                            <p>Looks like you haven't added any items to your cart yet.</p>
                            <a href="index.html#menu" class="btn btn-success mt-3">Browse Menu</a>
                        </div>
                    `);
                } else {
                    let cartHTML = '<div class="row">';
                    cartHTML += '<div class="col-md-8">';
                    
                    // Cart items
                    cartItems.forEach((item, index) => {
                        cartHTML += `
                            <div class="cart-item">
                                <div class="row">
                                    <div class="col-md-3">
                                        <img src="${item.image}" alt="${item.name}" class="img-fluid">
                                    </div>
                                    <div class="col-md-9">
                                        <div class="d-flex justify-content-between">
                                            <h4>${item.name}</h4>
                                            <h5>$${(item.price * item.quantity).toFixed(2)}</h5>
                                        </div>
                                        <p class="text-muted">${item.description}</p>
                                        <div class="d-flex justify-content-between align-items-center">
                                            <div class="input-group" style="max-width: 150px;">
                                                <div class="input-group-prepend">
                                                    <button class="btn btn-outline-secondary decrease-qty" data-index="${index}">-</button>
                                                </div>
                                                <input type="text" class="form-control text-center" value="${item.quantity}" readonly>
                                                <div class="input-group-append">
                                                    <button class="btn btn-outline-secondary increase-qty" data-index="${index}">+</button>
                                                </div>
                                            </div>
                                            <button class="btn btn-danger remove-item" data-index="${index}">
                                                <i class="fas fa-trash"></i> Remove
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        `;
                    });
                    
                    cartHTML += '</div>';
                    
                    // Cart summary
                    let subtotal = 0;
                    cartItems.forEach(item => {
                        subtotal += item.price * item.quantity;
                    });
                    
                    const tax = subtotal * 0.08;
                    const total = subtotal + tax;
                    
                    cartHTML += `
                        <div class="col-md-4">
                            <div class="cart-summary">
                                <h4 class="mb-3">Order Summary</h4>
                                <div class="d-flex justify-content-between mb-2">
                                    <span>Subtotal:</span>
                                    <span>$${subtotal.toFixed(2)}</span>
                                </div>
                                <div class="d-flex justify-content-between mb-2">
                                    <span>Tax (8%):</span>
                                    <span>$${tax.toFixed(2)}</span>
                                </div>
                                <hr>
                                <div class="d-flex justify-content-between mb-4">
                                    <strong>Total:</strong>
                                    <strong>$${total.toFixed(2)}</strong>
                                </div>
                                <button class="btn btn-success btn-block mb-2">Proceed to Checkout</button>
                                <a href="index.html#menu" class="btn btn-outline-secondary btn-block">Continue Shopping</a>
                            </div>
                        </div>
                    `;
                    
                    cartHTML += '</div>';
                    $('#cartContent').html(cartHTML);
                    
                    // Add event listeners for cart item buttons
                    $('.increase-qty').on('click', function() {
                        const index = $(this).data('index');
                        cartItems[index].quantity += 1;
                        localStorage.setItem('smoothieCart', JSON.stringify(cartItems));
                        loadCart();
                    });
                    
                    $('.decrease-qty').on('click', function() {
                        const index = $(this).data('index');
                        if (cartItems[index].quantity > 1) {
                            cartItems[index].quantity -= 1;
                            localStorage.setItem('smoothieCart', JSON.stringify(cartItems));
                            loadCart();
                        }
                    });
                    
                    $('.remove-item').on('click', function() {
                        const index = $(this).data('index');
                        cartItems.splice(index, 1);
                        localStorage.setItem('smoothieCart', JSON.stringify(cartItems));
                        loadCart();
                    });
                }
            }
            
            // Load cart on page load
            loadCart();
        });
    </script>
</body>
</html>
