import React, { useState } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  Row,
  Col,
  Input,
  FormGroup,
  Label,
  Badge
} from 'reactstrap';

const QuickView = ({ isOpen, toggle, item, addToCart }) => {
  const [quantity, setQuantity] = useState(1);
  const [selectedOptions, setSelectedOptions] = useState({});
  const [specialInstructions, setSpecialInstructions] = useState('');

  if (!item) return null;

  const handleQuantityChange = (e) => {
    const value = parseInt(e.target.value);
    if (value > 0) {
      setQuantity(value);
    }
  };

  const handleOptionChange = (optionType, optionValue) => {
    setSelectedOptions({
      ...selectedOptions,
      [optionType]: optionValue
    });
  };

  const handleAddToCart = () => {
    const cartItem = {
      ...item,
      quantity,
      selectedOptions,
      specialInstructions,
      totalPrice: calculateTotalPrice()
    };
    
    addToCart(cartItem);
    resetForm();
    toggle();
  };

  const resetForm = () => {
    setQuantity(1);
    setSelectedOptions({});
    setSpecialInstructions('');
  };

  const calculateTotalPrice = () => {
    let total = item.price;
    
    // Add price for options
    if (item.options) {
      Object.keys(selectedOptions).forEach(optionType => {
        const option = item.options[optionType].find(opt => opt.name === selectedOptions[optionType]);
        if (option && option.additionalPrice) {
          total += option.additionalPrice;
        }
      });
    }
    
    return total * quantity;
  };

  return (
    <Modal isOpen={isOpen} toggle={toggle} size="lg">
      <ModalHeader toggle={toggle}>{item.name}</ModalHeader>
      <ModalBody>
        <Row>
          <Col md={6}>
            <img 
              src={item.image} 
              alt={item.name} 
              className="img-fluid rounded mb-3" 
              style={{ maxHeight: '300px', width: '100%', objectFit: 'cover' }}
            />
          </Col>
          <Col md={6}>
            <h4>{item.name}</h4>
            <p className="text-muted">{item.description}</p>
            
            <div className="mb-3">
              <span className="h5 text-success">${item.price.toFixed(2)}</span>
              {item.nutritionalInfo && (
                <Badge color="light" className="ml-2">
                  {item.nutritionalInfo.calories} calories
                </Badge>
              )}
            </div>
            
            {item.options && Object.keys(item.options).map((optionType, index) => (
              <FormGroup key={index}>
                <Label for={`option-${optionType}`}>{optionType}</Label>
                <div>
                  {item.options[optionType].map((option, i) => (
                    <FormGroup check inline key={i}>
                      <Label check>
                        <Input
                          type="radio"
                          name={`option-${optionType}`}
                          value={option.name}
                          checked={selectedOptions[optionType] === option.name}
                          onChange={() => handleOptionChange(optionType, option.name)}
                        />
                        {option.name}
                        {option.additionalPrice > 0 && ` (+$${option.additionalPrice.toFixed(2)})`}
                      </Label>
                    </FormGroup>
                  ))}
                </div>
              </FormGroup>
            ))}
            
            <FormGroup>
              <Label for="quantity">Quantity</Label>
              <Input
                type="number"
                name="quantity"
                id="quantity"
                value={quantity}
                onChange={handleQuantityChange}
                min="1"
                max="10"
              />
            </FormGroup>
            
            <FormGroup>
              <Label for="specialInstructions">Special Instructions</Label>
              <Input
                type="textarea"
                name="specialInstructions"
                id="specialInstructions"
                value={specialInstructions}
                onChange={(e) => setSpecialInstructions(e.target.value)}
                placeholder="Any special requests?"
                rows="3"
              />
            </FormGroup>
          </Col>
        </Row>
      </ModalBody>
      <ModalFooter>
        <div className="mr-auto">
          <h5>Total: ${calculateTotalPrice().toFixed(2)}</h5>
        </div>
        <Button color="secondary" onClick={toggle}>Cancel</Button>
        <Button color="success" onClick={handleAddToCart}>
          <i className="fas fa-cart-plus mr-1"></i> Add to Cart
        </Button>
      </ModalFooter>
    </Modal>
  );
};

export default QuickView;
