<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Smoothie Vue API Test</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f8f9fa;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #27ae60;
            text-align: center;
            margin-bottom: 30px;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #dee2e6;
            border-radius: 8px;
        }
        .test-section h3 {
            color: #2c3e50;
            margin-bottom: 15px;
        }
        button {
            background-color: #27ae60;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        button:hover {
            background-color: #219a52;
        }
        .result {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin-top: 15px;
            white-space: pre-wrap;
            font-family: monospace;
            max-height: 300px;
            overflow-y: auto;
        }
        .success {
            border-color: #28a745;
            background-color: #d4edda;
        }
        .error {
            border-color: #dc3545;
            background-color: #f8d7da;
        }
        .status {
            display: inline-block;
            padding: 5px 10px;
            border-radius: 3px;
            font-weight: bold;
            margin-bottom: 10px;
        }
        .status.online {
            background-color: #d4edda;
            color: #155724;
        }
        .status.offline {
            background-color: #f8d7da;
            color: #721c24;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🥤 Smoothie Vue API Test</h1>
        
        <div class="test-section">
            <h3>🔗 API Connection Status</h3>
            <div id="api-status" class="status">Checking...</div>
            <button onclick="checkApiHealth()">Check API Health</button>
            <div id="health-result" class="result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>🍹 Food Items API</h3>
            <button onclick="testGetFoods()">Get All Foods</button>
            <button onclick="testGetCategories()">Get Categories</button>
            <button onclick="testSearchFoods()">Search Foods</button>
            <div id="foods-result" class="result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>👤 User API</h3>
            <button onclick="testCreateUser()">Test Create User</button>
            <button onclick="testLoginUser()">Test Login</button>
            <div id="user-result" class="result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>🛒 Cart API</h3>
            <button onclick="testCartOperations()">Test Cart Operations</button>
            <div id="cart-result" class="result" style="display: none;"></div>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:8001/api';
        
        // Utility function to make API calls
        async function apiCall(endpoint, options = {}) {
            try {
                const response = await fetch(`${API_BASE}${endpoint}`, {
                    headers: {
                        'Content-Type': 'application/json',
                        ...options.headers
                    },
                    ...options
                });
                
                const data = await response.json();
                return {
                    success: response.ok,
                    status: response.status,
                    data: data
                };
            } catch (error) {
                return {
                    success: false,
                    error: error.message
                };
            }
        }

        // Display result in a div
        function displayResult(elementId, result, isSuccess = true) {
            const element = document.getElementById(elementId);
            element.style.display = 'block';
            element.className = `result ${isSuccess ? 'success' : 'error'}`;
            element.textContent = JSON.stringify(result, null, 2);
        }

        // Check API Health
        async function checkApiHealth() {
            const statusElement = document.getElementById('api-status');
            statusElement.textContent = 'Checking...';
            
            const result = await apiCall('/health');
            
            if (result.success) {
                statusElement.textContent = 'API Online ✅';
                statusElement.className = 'status online';
                displayResult('health-result', result.data, true);
            } else {
                statusElement.textContent = 'API Offline ❌';
                statusElement.className = 'status offline';
                displayResult('health-result', result, false);
            }
        }

        // Test Foods API
        async function testGetFoods() {
            const result = await apiCall('/foods');
            displayResult('foods-result', result, result.success);
        }

        async function testGetCategories() {
            const result = await apiCall('/categories');
            displayResult('foods-result', result, result.success);
        }

        async function testSearchFoods() {
            const result = await apiCall('/foods/search?q=smoothie');
            displayResult('foods-result', result, result.success);
        }

        // Test User API
        async function testCreateUser() {
            const testUser = {
                user_name: 'Test User',
                user_email: '<EMAIL>',
                user_password: 'password123',
                user_phone: '555-0123'
            };
            
            const result = await apiCall('/users', {
                method: 'POST',
                body: JSON.stringify(testUser)
            });
            
            displayResult('user-result', result, result.success);
        }

        async function testLoginUser() {
            const credentials = {
                user_email: '<EMAIL>',
                user_password: 'password123'
            };
            
            const result = await apiCall('/users/login', {
                method: 'POST',
                body: JSON.stringify(credentials)
            });
            
            displayResult('user-result', result, result.success);
        }

        // Test Cart API
        async function testCartOperations() {
            const cartItem = {
                user_id: 1,
                food_id: 1,
                item_qty: 2
            };
            
            const result = await apiCall('/cart', {
                method: 'POST',
                body: JSON.stringify(cartItem)
            });
            
            displayResult('cart-result', result, result.success);
        }

        // Auto-check API health on page load
        window.onload = function() {
            checkApiHealth();
        };
    </script>
</body>
</html>
