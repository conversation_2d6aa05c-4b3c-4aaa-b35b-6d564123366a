import React from 'react';
import { 
  Card, 
  CardBody, 
  CardTitle, 
  CardSubtitle, 
  Table, 
  Badge,
  ListGroup,
  ListGroupItem
} from 'reactstrap';

const OrderDetails = ({ order }) => {
  if (!order) {
    return (
      <Card className="mb-4">
        <CardBody>
          <CardTitle tag="h5">Order Details</CardTitle>
          <p className="text-muted">No order selected</p>
        </CardBody>
      </Card>
    );
  }

  const getStatusBadge = (status) => {
    switch (status.toLowerCase()) {
      case 'pending':
        return <Badge color="warning">Pending</Badge>;
      case 'confirmed':
        return <Badge color="info">Confirmed</Badge>;
      case 'preparing':
        return <Badge color="primary">Preparing</Badge>;
      case 'ready':
        return <Badge color="success">Ready</Badge>;
      case 'delivered':
        return <Badge color="success">Delivered</Badge>;
      case 'cancelled':
        return <Badge color="danger">Cancelled</Badge>;
      default:
        return <Badge color="secondary">{status}</Badge>;
    }
  };

  const formatDate = (dateString) => {
    const options = { 
      year: 'numeric', 
      month: 'long', 
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    };
    return new Date(dateString).toLocaleDateString(undefined, options);
  };

  return (
    <Card className="mb-4">
      <CardBody>
        <CardTitle tag="h5">Order #{order.id}</CardTitle>
        <CardSubtitle tag="h6" className="mb-3 text-muted">
          {formatDate(order.date)}
        </CardSubtitle>
        
        <div className="mb-3">
          <strong>Status:</strong> {getStatusBadge(order.status)}
        </div>
        
        <div className="mb-3">
          <strong>Order Type:</strong> {order.type === 'delivery' ? 'Delivery' : 'Pickup'}
        </div>
        
        {order.type === 'delivery' && (
          <div className="mb-3">
            <strong>Delivery Address:</strong>
            <p className="mb-0">{order.address}</p>
          </div>
        )}
        
        <div className="mb-4">
          <strong>Items:</strong>
          <Table responsive borderless size="sm" className="mt-2">
            <thead>
              <tr>
                <th>Item</th>
                <th className="text-center">Qty</th>
                <th className="text-right">Price</th>
                <th className="text-right">Total</th>
              </tr>
            </thead>
            <tbody>
              {order.items.map((item, index) => (
                <tr key={index}>
                  <td>{item.name}</td>
                  <td className="text-center">{item.quantity}</td>
                  <td className="text-right">${item.price.toFixed(2)}</td>
                  <td className="text-right">${(item.price * item.quantity).toFixed(2)}</td>
                </tr>
              ))}
            </tbody>
          </Table>
        </div>
        
        <ListGroup className="mb-3">
          <ListGroupItem className="d-flex justify-content-between">
            <span>Subtotal:</span>
            <span>${order.subtotal.toFixed(2)}</span>
          </ListGroupItem>
          <ListGroupItem className="d-flex justify-content-between">
            <span>Tax:</span>
            <span>${order.tax.toFixed(2)}</span>
          </ListGroupItem>
          {order.type === 'delivery' && (
            <ListGroupItem className="d-flex justify-content-between">
              <span>Delivery Fee:</span>
              <span>${order.deliveryFee.toFixed(2)}</span>
            </ListGroupItem>
          )}
          {order.discount > 0 && (
            <ListGroupItem className="d-flex justify-content-between text-success">
              <span>Discount:</span>
              <span>-${order.discount.toFixed(2)}</span>
            </ListGroupItem>
          )}
          <ListGroupItem className="d-flex justify-content-between font-weight-bold">
            <span>Total:</span>
            <span>${order.total.toFixed(2)}</span>
          </ListGroupItem>
        </ListGroup>
        
        <div>
          <strong>Payment Method:</strong> {order.paymentMethod}
        </div>
      </CardBody>
    </Card>
  );
};

export default OrderDetails;
