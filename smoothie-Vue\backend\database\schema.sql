-- Smoothie Vue Database Schema
-- Database name: "db_smoothie_vue"

-- Create database
CREATE DATABASE IF NOT EXISTS db_smoothie_vue;
USE db_smoothie_vue;

-- Food/Menu Items Table
CREATE TABLE food (
    food_id INT(11) PRIMARY KEY AUTO_INCREMENT,
    food_name VARCHAR(255) NOT NULL,
    food_star DECIMAL(2,1) DEFAULT 4.0,
    food_vote VARCHAR(255) DEFAULT '0',
    food_price DECIMAL(10,2) NOT NULL,
    food_discount DECIMAL(10,2) DEFAULT 0.00,
    food_desc TEXT,
    food_status VARCHAR(255) DEFAULT 'available',
    food_type VARCHAR(255) DEFAULT 'smoothie',
    food_category VARCHAR(255) NOT NULL,
    food_src VARCHAR(255),
    food_ingredients TEXT,
    food_nutrition TEXT,
    food_allergens VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=INNODB;

-- Users Table
CREATE TABLE user (
    user_id INT(11) PRIMARY KEY AUTO_INCREMENT,
    user_name VARCHAR(255) NOT NULL,
    user_email VARCHAR(255) UNIQUE NOT NULL,
    user_phone VARCHAR(255),
    user_password VARCHAR(255) NOT NULL,
    user_birth DATE,
    user_gender ENUM('male', 'female', 'other'),
    user_address TEXT,
    user_preferences TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=INNODB;

-- Cart Table
CREATE TABLE cart (
    user_id INT(11),
    food_id INT(11),
    item_qty INT(11) DEFAULT 1,
    customizations TEXT,
    added_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (user_id, food_id),
    FOREIGN KEY (user_id) REFERENCES user(user_id) ON DELETE CASCADE,
    FOREIGN KEY (food_id) REFERENCES food(food_id) ON DELETE CASCADE
) ENGINE=INNODB;

-- Orders Table
CREATE TABLE orders (
    order_id INT(11) PRIMARY KEY AUTO_INCREMENT,
    user_id INT(11),
    order_total DECIMAL(10,2) NOT NULL,
    order_status ENUM('pending', 'confirmed', 'preparing', 'ready', 'completed', 'cancelled') DEFAULT 'pending',
    payment_method VARCHAR(255),
    payment_status ENUM('pending', 'paid', 'failed', 'refunded') DEFAULT 'pending',
    delivery_type ENUM('pickup', 'delivery') DEFAULT 'pickup',
    delivery_address TEXT,
    delivery_fee DECIMAL(10,2) DEFAULT 0.00,
    special_instructions TEXT,
    estimated_time INT(11),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES user(user_id) ON DELETE SET NULL
) ENGINE=INNODB;

-- Order Items Table
CREATE TABLE order_items (
    order_item_id INT(11) PRIMARY KEY AUTO_INCREMENT,
    order_id INT(11),
    food_id INT(11),
    quantity INT(11) NOT NULL,
    unit_price DECIMAL(10,2) NOT NULL,
    customizations TEXT,
    FOREIGN KEY (order_id) REFERENCES orders(order_id) ON DELETE CASCADE,
    FOREIGN KEY (food_id) REFERENCES food(food_id) ON DELETE SET NULL
) ENGINE=INNODB;

-- Categories Table (for better organization)
CREATE TABLE categories (
    category_id INT(11) PRIMARY KEY AUTO_INCREMENT,
    category_name VARCHAR(255) UNIQUE NOT NULL,
    category_description TEXT,
    category_image VARCHAR(255),
    display_order INT(11) DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
) ENGINE=INNODB;

-- Insert default categories
INSERT INTO categories (category_name, category_description, display_order) VALUES
('smoothie', 'Fresh fruit and vegetable smoothies', 1),
('bowl', 'Smoothie bowls with toppings', 2),
('juice', 'Fresh pressed juices', 3),
('protein', 'Protein shakes and supplements', 4),
('add-ons', 'Extra toppings and supplements', 5);

-- Sample smoothie data
INSERT INTO food (food_name, food_star, food_vote, food_price, food_discount, food_desc, food_status, food_type, food_category, food_src) VALUES
('Tropical Paradise', 4.8, '156', 8.99, 0.00, 'Mango, pineapple, coconut milk, and banana', 'best seller', 'smoothie', 'smoothie', 'tropical-smoothie.jpg'),
('Berry Blast', 4.7, '203', 7.99, 1.00, 'Mixed berries, yogurt, and honey', 'best seller', 'smoothie', 'smoothie', 'berry-smoothie.jpg'),
('Green Goddess', 4.5, '89', 9.99, 0.00, 'Spinach, kale, apple, cucumber, and lemon', 'new', 'smoothie', 'smoothie', 'green-smoothie.jpg'),
('Chocolate Protein Power', 4.6, '134', 11.99, 2.00, 'Chocolate protein, banana, almond milk, and peanut butter', 'best seller', 'protein', 'protein', 'chocolate-protein.jpg'),
('Vanilla Dream', 4.4, '78', 10.99, 0.00, 'Vanilla protein, oats, banana, and cinnamon', 'available', 'protein', 'protein', 'vanilla-protein.jpg'),
('Acai Power Bowl', 4.9, '245', 12.99, 0.00, 'Acai, granola, fresh berries, and coconut flakes', 'best seller', 'bowl', 'bowl', 'acai-bowl.jpg'),
('Tropical Bowl', 4.6, '167', 11.99, 1.50, 'Mango, pineapple, granola, and chia seeds', 'available', 'bowl', 'bowl', 'tropical-bowl.jpg'),
('Orange Sunrise', 4.3, '92', 6.99, 0.00, 'Fresh orange, carrot, and ginger', 'available', 'juice', 'juice', 'juice.jpg'),
('Green Detox', 4.5, '115', 7.99, 0.00, 'Celery, cucumber, apple, and lemon', 'new', 'juice', 'juice', 'green-smoothie.jpg'),
('Pink Lemonade', 4.2, '67', 5.99, 0.50, 'Lemon, strawberry, and mint', 'seasonal', 'juice', 'juice', 'lemonade.jpg');
