// Cart Model
import db from "../config/database.js";

// Get all cart items for a user
export const getAllItems = (userId, result) => {
    db.query(
        `SELECT c.*, f.food_name, f.food_price, f.food_discount, f.food_src, f.food_desc 
         FROM cart c 
         JOIN food f ON c.food_id = f.food_id 
         WHERE c.user_id = ?`, 
        [userId], 
        (err, results) => {
            if (err) {
                console.error("Error fetching cart items:", err);
                result(err, null);
            } else {
                result(null, results);
            }
        }
    );
};

// Get specific cart item
export const getAItem = (userId, foodId, result) => {
    db.query("SELECT * FROM cart WHERE user_id = ? AND food_id = ?", [userId, foodId], (err, results) => {
        if (err) {
            console.error("Error fetching cart item:", err);
            result(err, null);
        } else {
            result(null, results);
        }
    });
};

// Add item to cart or update quantity if exists
export const insertToCart = (data, result) => {
    // First check if item already exists
    db.query("SELECT * FROM cart WHERE user_id = ? AND food_id = ?", [data.user_id, data.food_id], (err, existing) => {
        if (err) {
            console.error("Error checking existing cart item:", err);
            result(err, null);
            return;
        }

        if (existing.length > 0) {
            // Update existing item quantity
            const newQuantity = existing[0].item_qty + (data.item_qty || 1);
            db.query("UPDATE cart SET item_qty = ? WHERE user_id = ? AND food_id = ?", 
                [newQuantity, data.user_id, data.food_id], (updateErr, updateResults) => {
                if (updateErr) {
                    console.error("Error updating cart item:", updateErr);
                    result(updateErr, null);
                } else {
                    result(null, updateResults);
                }
            });
        } else {
            // Insert new item
            db.query("INSERT INTO cart SET ?", data, (insertErr, insertResults) => {
                if (insertErr) {
                    console.error("Error inserting cart item:", insertErr);
                    result(insertErr, null);
                } else {
                    result(null, insertResults);
                }
            });
        }
    });
};

// Update cart item quantity
export const updateCartItemQty = (data, result) => {
    db.query("UPDATE cart SET item_qty = ? WHERE user_id = ? AND food_id = ?", 
        [data.item_qty, data.user_id, data.food_id], (err, results) => {
        if (err) {
            console.error("Error updating cart item quantity:", err);
            result(err, null);
        } else {
            result(null, results);
        }
    });
};

// Delete specific item from cart
export const deleteItemInCart = (userId, foodId, result) => {
    db.query("DELETE FROM cart WHERE user_id = ? AND food_id = ?", [userId, foodId], (err, results) => {
        if (err) {
            console.error("Error deleting cart item:", err);
            result(err, null);
        } else {
            result(null, results);
        }
    });
};

// Delete all items from user's cart
export const deleteAllItemsByUser = (userId, result) => {
    db.query("DELETE FROM cart WHERE user_id = ?", [userId], (err, results) => {
        if (err) {
            console.error("Error clearing cart:", err);
            result(err, null);
        } else {
            result(null, results);
        }
    });
};

// Get cart summary (total items and price)
export const getCartSummary = (userId, result) => {
    db.query(
        `SELECT 
            COUNT(*) as total_items,
            SUM(c.item_qty) as total_quantity,
            SUM(c.item_qty * (f.food_price - f.food_discount)) as total_price
         FROM cart c 
         JOIN food f ON c.food_id = f.food_id 
         WHERE c.user_id = ?`, 
        [userId], 
        (err, results) => {
            if (err) {
                console.error("Error fetching cart summary:", err);
                result(err, null);
            } else {
                result(null, results[0]);
            }
        }
    );
};
