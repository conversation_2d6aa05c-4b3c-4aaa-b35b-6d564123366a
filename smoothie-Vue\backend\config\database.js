import mysql from "mysql2";
import dotenv from "dotenv";

// Load environment variables
dotenv.config();

// Create the connection to database
const db = mysql.createConnection({
    host: process.env.DB_HOST || "localhost",
    user: process.env.DB_USER || "root",
    password: process.env.DB_PASSWORD || "",
    database: process.env.DB_NAME || "db_smoothie_vue"
});

db.connect(error => {
  if (error) {
    console.error("Database connection failed:", error);
    console.log("⚠️  Server will continue without database connection");
    console.log("💡 Make sure MySQL is running and database exists");
    // Don't throw error, let server start anyway
  } else {
    console.log("✅ Successfully connected to the database.");
  }
});

export default db;
