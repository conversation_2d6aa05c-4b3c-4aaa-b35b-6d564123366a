# RestaurantReactApp
<hr/>
<h2> Structure </h2>
<ul>
  <li> Used create-react-app and cra-template for structure</li>
  <li> Components are <b>functions</b> </li>
  <li> Containers are <b>Classes</b> </li>
  <li> Completely <b>Responsive</b> </li>
  <li> Uses <b>Firebase Realtime DataBase</b> </li>
  <li> Completely <b>Dynamic website</b> </li>
  <li> Routing with <b>React Router</b> </li>
 </ul>
 <h2> Working </h2>
 <p> The complete data is getting requested from firebase Realtime database, the components use that data to display the results, the components in menu, offers and other sections can then directly be modified by simply modifying the data in database, for example if the price of something changes or the image of some dish changes you don't have to modify the code, simply make the change in databse and it will be reflected on the website. For the routing purpose react-router is used and specifically hashBrowserRouter is used because it has enchanced performance on react Hosting. The hosting is done on firebase at <a href="https://twobrother-0927.firebaseapp.com/#/" target="_blank"> <abbr title="Link to my website"> Website </abbr> </a> </p> 
 <p> I created this website last year for a local shop owner but later on when I didn't sell it, I thought to make it open source and here it is. Feel free to use the provided components and code in your website just provide the copyright </p> 
