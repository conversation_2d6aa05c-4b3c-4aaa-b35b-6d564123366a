import axios from 'axios'

// Create axios instance with default configuration
const apiClient = axios.create({
  baseURL: process.env.VUE_APP_API_URL || '/api',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json'
  }
})

// Request interceptor
apiClient.interceptors.request.use(
  (config) => {
    // Add auth token if available
    const token = localStorage.getItem('auth_token')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    
    // Add request timestamp for debugging
    config.metadata = { startTime: new Date() }
    
    // Log request in development
    if (process.env.NODE_ENV === 'development') {
      console.log(`🚀 API Request: ${config.method?.toUpperCase()} ${config.url}`, {
        data: config.data,
        params: config.params
      })
    }
    
    return config
  },
  (error) => {
    console.error('Request interceptor error:', error)
    return Promise.reject(error)
  }
)

// Response interceptor
apiClient.interceptors.response.use(
  (response) => {
    // Calculate request duration
    const duration = new Date() - response.config.metadata.startTime
    
    // Log response in development
    if (process.env.NODE_ENV === 'development') {
      console.log(`✅ API Response: ${response.config.method?.toUpperCase()} ${response.config.url} (${duration}ms)`, {
        status: response.status,
        data: response.data
      })
    }
    
    return response
  },
  (error) => {
    // Calculate request duration if available
    const duration = error.config?.metadata ? 
      new Date() - error.config.metadata.startTime : 'unknown'
    
    // Log error in development
    if (process.env.NODE_ENV === 'development') {
      console.error(`❌ API Error: ${error.config?.method?.toUpperCase()} ${error.config?.url} (${duration}ms)`, {
        status: error.response?.status,
        message: error.response?.data?.message || error.message,
        data: error.response?.data
      })
    }
    
    // Handle specific error cases
    if (error.response) {
      // Server responded with error status
      const { status, data } = error.response
      
      switch (status) {
        case 401:
          // Unauthorized - clear auth and redirect to login
          localStorage.removeItem('auth_token')
          localStorage.removeItem('user_data')
          if (window.location.pathname !== '/login') {
            window.location.href = '/login'
          }
          break
          
        case 403:
          // Forbidden
          console.warn('Access forbidden:', data.message)
          break
          
        case 404:
          // Not found
          console.warn('Resource not found:', error.config?.url)
          break
          
        case 422:
          // Validation error
          console.warn('Validation error:', data.errors || data.message)
          break
          
        case 500:
          // Server error
          console.error('Server error:', data.message)
          break
          
        default:
          console.error('API error:', data.message || 'Unknown error')
      }
    } else if (error.request) {
      // Network error
      console.error('Network error:', error.message)
    } else {
      // Other error
      console.error('Request setup error:', error.message)
    }
    
    return Promise.reject(error)
  }
)

// API service object
const apiService = {
  // Generic HTTP methods
  get(url, config = {}) {
    return apiClient.get(url, config)
  },
  
  post(url, data = {}, config = {}) {
    return apiClient.post(url, data, config)
  },
  
  put(url, data = {}, config = {}) {
    return apiClient.put(url, data, config)
  },
  
  patch(url, data = {}, config = {}) {
    return apiClient.patch(url, data, config)
  },
  
  delete(url, config = {}) {
    return apiClient.delete(url, config)
  },
  
  // Food/Menu API methods
  foods: {
    getAll() {
      return apiService.get('/foods')
    },
    
    getById(id) {
      return apiService.get(`/foods/${id}`)
    },
    
    getByCategory(category) {
      return apiService.get(`/foods/category/${category}`)
    },
    
    search(query) {
      return apiService.get('/foods/search', { params: { q: query } })
    },
    
    create(data) {
      return apiService.post('/foods', data)
    },
    
    update(id, data) {
      return apiService.put(`/foods/${id}`, data)
    },
    
    delete(id) {
      return apiService.delete(`/foods/${id}`)
    }
  },
  
  // User API methods
  users: {
    getAll() {
      return apiService.get('/users')
    },
    
    getById(id) {
      return apiService.get(`/users/${id}`)
    },
    
    getByEmail(email) {
      return apiService.get(`/users/email/${email}`)
    },
    
    create(data) {
      return apiService.post('/users', data)
    },
    
    update(id, data) {
      return apiService.put(`/users/${id}`, data)
    },
    
    delete(id) {
      return apiService.delete(`/users/${id}`)
    },
    
    login(credentials) {
      return apiService.post('/users/login', credentials)
    }
  },
  
  // Cart API methods
  cart: {
    getItems(userId) {
      return apiService.get(`/cart/${userId}`)
    },
    
    getSummary(userId) {
      return apiService.get(`/cart/${userId}/summary`)
    },
    
    getItem(userId, foodId) {
      return apiService.get(`/cart/${userId}/${foodId}`)
    },
    
    addItem(data) {
      return apiService.post('/cart', data)
    },
    
    updateItem(data) {
      return apiService.put('/cart', data)
    },
    
    removeItem(userId, foodId) {
      return apiService.delete(`/cart/${userId}/${foodId}`)
    },
    
    clear(userId) {
      return apiService.delete(`/cart/${userId}`)
    }
  },
  
  // Utility API methods
  categories: {
    getAll() {
      return apiService.get('/categories')
    }
  },
  
  stats: {
    getMenu() {
      return apiService.get('/stats/menu')
    }
  },
  
  // Health check
  health() {
    return apiService.get('/health')
  }
}

export default apiService
