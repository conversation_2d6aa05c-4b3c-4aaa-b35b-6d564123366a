import React, { Component } from 'react';
import { Container, Row, Col, Card, CardImg, CardBody, CardTitle, CardText, Button } from 'reactstrap';
import { Link } from 'react-router-dom';
import Navigation from '../../Components/Navigation/Navigation';
import Footer from '../../Components/Footer/Footer';

class Offer extends Component {
  render() {
    const { data, board, count } = this.props;
    
    return (
      <div>
        <Navigation cartCount={count} />
        
        <div className="bg-light py-5">
          <Container className="text-center">
            <h1>{board.title}</h1>
            <p className="lead">{board.subtitle}</p>
          </Container>
        </div>
        
        <Container className="py-5">
          <Row>
            {data && data.map(offer => (
              <Col md={4} key={offer.id} className="mb-4">
                <Card className="h-100">
                  <CardImg top width="100%" src={offer.image} alt={offer.title} />
                  <CardBody>
                    <CardTitle tag="h3">{offer.title}</CardTitle>
                    <CardText>{offer.description}</CardText>
                    <div className="d-flex justify-content-between align-items-center">
                      <span className="badge badge-success p-2">Code: {offer.code}</span>
                      <Button 
                        color="primary" 
                        tag={Link} 
                        to="/menu"
                      >
                        Use Offer
                      </Button>
                    </div>
                  </CardBody>
                </Card>
              </Col>
            ))}
          </Row>
        </Container>
        
        <div className="bg-light py-5">
          <Container className="text-center">
            <h2>How to Redeem Offers</h2>
            <Row className="mt-4">
              <Col md={4}>
                <div className="mb-3">
                  <i className="fas fa-search fa-3x text-success"></i>
                </div>
                <h4>1. Find an Offer</h4>
                <p>Browse our special offers and find one you like.</p>
              </Col>
              <Col md={4}>
                <div className="mb-3">
                  <i className="fas fa-copy fa-3x text-success"></i>
                </div>
                <h4>2. Copy the Code</h4>
                <p>Note down the offer code you want to use.</p>
              </Col>
              <Col md={4}>
                <div className="mb-3">
                  <i className="fas fa-check-circle fa-3x text-success"></i>
                </div>
                <h4>3. Apply at Checkout</h4>
                <p>Enter the code during checkout to get your discount.</p>
              </Col>
            </Row>
            <Button 
              color="success" 
              size="lg" 
              tag={Link} 
              to="/menu"
              className="mt-4"
            >
              Browse Menu
            </Button>
          </Container>
        </div>
        
        <Footer />
      </div>
    );
  }
}

export default Offer;
