{"name": "smoothie-vue-backend", "version": "1.0.0", "description": "Smoothie Vue Restaurant Ordering System Backend", "type": "module", "main": "index.js", "scripts": {"start": "node index.js", "dev": "nodemon index.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["smoothie", "restaurant", "ordering", "vue", "express"], "author": "Smoothie Vue Team", "license": "MIT", "dependencies": {"body-parser": "^1.20.0", "cors": "^2.8.5", "express": "^4.18.1", "mysql2": "^2.3.3", "dotenv": "^16.0.0"}, "devDependencies": {"nodemon": "^2.0.19"}}