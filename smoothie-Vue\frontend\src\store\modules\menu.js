// Menu Store Module
const state = {
  foods: [],
  categories: [],
  featuredItems: [],
  currentCategory: null,
  searchQuery: '',
  isLoading: false,
  error: null
}

const getters = {
  allFoods: state => state.foods,
  foodsByCategory: state => category => {
    return state.foods.filter(food => food.food_category === category)
  },
  featuredFoods: state => state.featuredItems,
  searchResults: state => {
    if (!state.searchQuery) return state.foods
    return state.foods.filter(food => 
      food.food_name.toLowerCase().includes(state.searchQuery.toLowerCase()) ||
      food.food_desc.toLowerCase().includes(state.searchQuery.toLowerCase())
    )
  },
  categories: state => state.categories,
  isLoading: state => state.isLoading
}

const mutations = {
  SET_FOODS(state, foods) {
    state.foods = foods
  },
  
  SET_CATEGORIES(state, categories) {
    state.categories = categories
  },
  
  SET_FEATURED_ITEMS(state, items) {
    state.featuredItems = items
  },
  
  SET_CURRENT_CATEGORY(state, category) {
    state.currentCategory = category
  },
  
  SET_SEARCH_QUERY(state, query) {
    state.searchQuery = query
  },
  
  SET_LOADING(state, loading) {
    state.isLoading = loading
  },
  
  SET_ERROR(state, error) {
    state.error = error
  },
  
  ADD_FOOD(state, food) {
    state.foods.push(food)
  },
  
  UPDATE_FOOD(state, updatedFood) {
    const index = state.foods.findIndex(food => food.food_id === updatedFood.food_id)
    if (index !== -1) {
      state.foods.splice(index, 1, updatedFood)
    }
  },
  
  REMOVE_FOOD(state, foodId) {
    state.foods = state.foods.filter(food => food.food_id !== foodId)
  }
}

const actions = {
  // Load all foods
  async loadFoods({ commit }) {
    try {
      commit('SET_LOADING', true)
      commit('SET_ERROR', null)
      
      // Mock data for now - replace with API call
      const mockFoods = [
        {
          food_id: 1,
          food_name: 'Tropical Paradise',
          food_star: 4.8,
          food_vote: '156',
          food_price: 8.99,
          food_discount: 0.00,
          food_desc: 'Mango, pineapple, coconut milk, and banana',
          food_status: 'best seller',
          food_type: 'smoothie',
          food_category: 'smoothie',
          food_src: 'tropical-smoothie.jpg'
        },
        {
          food_id: 2,
          food_name: 'Berry Blast',
          food_star: 4.7,
          food_vote: '203',
          food_price: 7.99,
          food_discount: 1.00,
          food_desc: 'Mixed berries, yogurt, and honey',
          food_status: 'best seller',
          food_type: 'smoothie',
          food_category: 'smoothie',
          food_src: 'berry-smoothie.jpg'
        },
        {
          food_id: 3,
          food_name: 'Green Goddess',
          food_star: 4.5,
          food_vote: '89',
          food_price: 9.99,
          food_discount: 0.00,
          food_desc: 'Spinach, kale, apple, cucumber, and lemon',
          food_status: 'new',
          food_type: 'smoothie',
          food_category: 'smoothie',
          food_src: 'green-smoothie.jpg'
        },
        {
          food_id: 4,
          food_name: 'Acai Power Bowl',
          food_star: 4.9,
          food_vote: '245',
          food_price: 12.99,
          food_discount: 0.00,
          food_desc: 'Acai, granola, fresh berries, and coconut flakes',
          food_status: 'best seller',
          food_type: 'bowl',
          food_category: 'bowl',
          food_src: 'acai-bowl.jpg'
        }
      ]
      
      commit('SET_FOODS', mockFoods)
      
    } catch (error) {
      commit('SET_ERROR', error.message)
      console.error('Error loading foods:', error)
    } finally {
      commit('SET_LOADING', false)
    }
  },
  
  // Load categories
  async loadCategories({ commit }) {
    try {
      const mockCategories = [
        { id: 'smoothie', name: 'Smoothies', description: 'Fresh fruit smoothies' },
        { id: 'bowl', name: 'Bowls', description: 'Smoothie bowls with toppings' },
        { id: 'juice', name: 'Juices', description: 'Fresh pressed juices' },
        { id: 'protein', name: 'Protein', description: 'Protein shakes' },
        { id: 'add-ons', name: 'Add-ons', description: 'Extra toppings' }
      ]
      
      commit('SET_CATEGORIES', mockCategories)
      
    } catch (error) {
      commit('SET_ERROR', error.message)
      console.error('Error loading categories:', error)
    }
  },
  
  // Load featured items
  async loadFeaturedItems({ commit, state }) {
    try {
      // Get featured items from existing foods or load them
      if (state.foods.length === 0) {
        await this.dispatch('menu/loadFoods')
      }
      
      const featured = state.foods.filter(food => 
        food.food_status === 'best seller' || food.food_status === 'new'
      ).slice(0, 6)
      
      commit('SET_FEATURED_ITEMS', featured)
      
    } catch (error) {
      commit('SET_ERROR', error.message)
      console.error('Error loading featured items:', error)
    }
  },
  
  // Search foods
  async searchFoods({ commit }, query) {
    commit('SET_SEARCH_QUERY', query)
    // The getter will handle the filtering
  },
  
  // Set current category
  setCurrentCategory({ commit }, category) {
    commit('SET_CURRENT_CATEGORY', category)
  },
  
  // Add new food (admin)
  async addFood({ commit }, foodData) {
    try {
      commit('SET_LOADING', true)
      
      // This would be an API call in a real app
      const newFood = {
        ...foodData,
        food_id: Date.now(), // Mock ID
        created_at: new Date().toISOString()
      }
      
      commit('ADD_FOOD', newFood)
      
      return { success: true, food: newFood }
      
    } catch (error) {
      commit('SET_ERROR', error.message)
      throw error
    } finally {
      commit('SET_LOADING', false)
    }
  },
  
  // Update food (admin)
  async updateFood({ commit }, { id, foodData }) {
    try {
      commit('SET_LOADING', true)
      
      // This would be an API call in a real app
      const updatedFood = {
        ...foodData,
        food_id: id,
        updated_at: new Date().toISOString()
      }
      
      commit('UPDATE_FOOD', updatedFood)
      
      return { success: true, food: updatedFood }
      
    } catch (error) {
      commit('SET_ERROR', error.message)
      throw error
    } finally {
      commit('SET_LOADING', false)
    }
  },
  
  // Delete food (admin)
  async deleteFood({ commit }, foodId) {
    try {
      commit('SET_LOADING', true)
      
      // This would be an API call in a real app
      commit('REMOVE_FOOD', foodId)
      
      return { success: true }
      
    } catch (error) {
      commit('SET_ERROR', error.message)
      throw error
    } finally {
      commit('SET_LOADING', false)
    }
  }
}

export default {
  namespaced: true,
  state,
  getters,
  mutations,
  actions
}
