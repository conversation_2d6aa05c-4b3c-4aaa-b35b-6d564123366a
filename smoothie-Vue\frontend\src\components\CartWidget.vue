<template>
  <div class="cart-widget">
    <div class="cart-widget-content">
      <div class="cart-header">
        <h6 class="mb-0">Shopping Cart</h6>
        <button class="btn-close" @click="closeCart" aria-label="Close cart"></button>
      </div>
      
      <div class="cart-body">
        <div v-if="cartItems.length === 0" class="empty-cart text-center py-4">
          <i class="fas fa-shopping-cart text-muted mb-3" style="font-size: 3rem;"></i>
          <p class="text-muted">Your cart is empty</p>
          <router-link to="/menu" class="btn btn-primary btn-sm" @click="closeCart">
            Browse Menu
          </router-link>
        </div>
        
        <div v-else>
          <div v-for="item in cartItems" :key="item.id" class="cart-item">
            <div class="d-flex align-items-center">
              <img :src="item.image || '/images/placeholder.jpg'" :alt="item.name" class="cart-item-image">
              <div class="flex-grow-1 ms-3">
                <h6 class="mb-1">{{ item.name }}</h6>
                <p class="text-muted mb-1 small">{{ formatCurrency(item.price) }}</p>
                <div class="quantity-controls">
                  <button class="btn btn-sm btn-outline-secondary" @click="decreaseQuantity(item)">-</button>
                  <span class="mx-2">{{ item.quantity }}</span>
                  <button class="btn btn-sm btn-outline-secondary" @click="increaseQuantity(item)">+</button>
                </div>
              </div>
              <button class="btn btn-sm btn-outline-danger" @click="removeItem(item)">
                <i class="fas fa-trash"></i>
              </button>
            </div>
          </div>
          
          <div class="cart-footer">
            <div class="cart-total">
              <strong>Total: {{ formatCurrency(cartTotal) }}</strong>
            </div>
            <div class="cart-actions mt-3">
              <router-link to="/cart" class="btn btn-outline-primary btn-sm me-2" @click="closeCart">
                View Cart
              </router-link>
              <router-link to="/checkout" class="btn btn-primary btn-sm" @click="closeCart">
                Checkout
              </router-link>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { mapState, mapGetters, mapActions } from 'vuex'

export default {
  name: 'CartWidget',
  
  computed: {
    ...mapState('cart', ['items']),
    ...mapGetters('cart', ['cartTotal', 'cartItemCount']),
    
    cartItems() {
      return this.items || []
    }
  },
  
  methods: {
    ...mapActions(['closeCartWidget']),
    ...mapActions('cart', ['updateItemQuantity', 'removeFromCart']),
    
    closeCart() {
      this.closeCartWidget()
    },
    
    increaseQuantity(item) {
      this.updateItemQuantity({
        id: item.id,
        quantity: item.quantity + 1
      })
    },
    
    decreaseQuantity(item) {
      if (item.quantity > 1) {
        this.updateItemQuantity({
          id: item.id,
          quantity: item.quantity - 1
        })
      }
    },
    
    removeItem(item) {
      this.removeFromCart(item.id)
    },
    
    formatCurrency(amount) {
      return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'USD'
      }).format(amount)
    }
  }
}
</script>

<style scoped>
.cart-widget {
  position: fixed;
  top: 80px;
  right: 20px;
  width: 350px;
  max-height: 500px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 8px 25px rgba(0,0,0,0.15);
  z-index: 1000;
  overflow: hidden;
}

.cart-widget-content {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.cart-header {
  display: flex;
  justify-content: between;
  align-items: center;
  padding: 1rem;
  border-bottom: 1px solid #eee;
  background-color: #f8f9fa;
}

.cart-body {
  flex: 1;
  overflow-y: auto;
  padding: 1rem;
}

.cart-item {
  padding: 0.75rem 0;
  border-bottom: 1px solid #eee;
}

.cart-item:last-child {
  border-bottom: none;
}

.cart-item-image {
  width: 50px;
  height: 50px;
  object-fit: cover;
  border-radius: 8px;
}

.quantity-controls {
  display: flex;
  align-items: center;
}

.quantity-controls button {
  width: 30px;
  height: 30px;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.cart-footer {
  border-top: 1px solid #eee;
  padding-top: 1rem;
}

.cart-total {
  text-align: center;
  font-size: 1.1rem;
}

.cart-actions {
  display: flex;
  gap: 0.5rem;
}

.cart-actions .btn {
  flex: 1;
}

/* Mobile responsive */
@media (max-width: 768px) {
  .cart-widget {
    right: 10px;
    width: calc(100vw - 20px);
    max-width: 350px;
  }
}
</style>
