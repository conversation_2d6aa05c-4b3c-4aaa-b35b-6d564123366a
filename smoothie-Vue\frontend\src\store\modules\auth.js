// Authentication Store Module
const state = {
  user: null,
  token: null,
  isAuthenticated: false,
  isLoading: false
}

const getters = {
  isAuthenticated: state => state.isAuthenticated,
  isAdmin: state => state.user?.role === 'admin',
  userName: state => state.user?.user_name || 'Guest',
  userId: state => state.user?.user_id,
  userEmail: state => state.user?.user_email
}

const mutations = {
  SET_USER(state, user) {
    state.user = user
    state.isAuthenticated = !!user
  },
  
  SET_TOKEN(state, token) {
    state.token = token
  },
  
  SET_LOADING(state, loading) {
    state.isLoading = loading
  },
  
  CLEAR_AUTH(state) {
    state.user = null
    state.token = null
    state.isAuthenticated = false
  }
}

const actions = {
  // Check for stored authentication
  checkStoredAuth({ commit }) {
    try {
      const token = localStorage.getItem('auth_token')
      const userData = localStorage.getItem('user_data')
      
      if (token && userData) {
        const user = JSON.parse(userData)
        commit('SET_TOKEN', token)
        commit('SET_USER', user)
      }
    } catch (error) {
      console.error('Error checking stored auth:', error)
      // Clear invalid stored data
      localStorage.removeItem('auth_token')
      localStorage.removeItem('user_data')
    }
  },
  
  // Login user
  async loginUser({ commit }, credentials) {
    try {
      commit('SET_LOADING', true)
      
      // This would be an API call in a real app
      // For now, we'll simulate a successful login
      const mockUser = {
        user_id: 1,
        user_name: 'Demo User',
        user_email: credentials.email,
        role: 'user'
      }
      
      const mockToken = 'demo_token_' + Date.now()
      
      // Store in localStorage
      localStorage.setItem('auth_token', mockToken)
      localStorage.setItem('user_data', JSON.stringify(mockUser))
      
      // Update state
      commit('SET_TOKEN', mockToken)
      commit('SET_USER', mockUser)
      
      return { success: true, user: mockUser }
      
    } catch (error) {
      console.error('Login error:', error)
      throw error
    } finally {
      commit('SET_LOADING', false)
    }
  },
  
  // Register user
  async registerUser({ commit }, userData) {
    try {
      commit('SET_LOADING', true)
      
      // This would be an API call in a real app
      // For now, we'll simulate a successful registration
      const newUser = {
        user_id: Date.now(),
        user_name: userData.name,
        user_email: userData.email,
        role: 'user'
      }
      
      const token = 'demo_token_' + Date.now()
      
      // Store in localStorage
      localStorage.setItem('auth_token', token)
      localStorage.setItem('user_data', JSON.stringify(newUser))
      
      // Update state
      commit('SET_TOKEN', token)
      commit('SET_USER', newUser)
      
      return { success: true, user: newUser }
      
    } catch (error) {
      console.error('Registration error:', error)
      throw error
    } finally {
      commit('SET_LOADING', false)
    }
  },
  
  // Logout user
  logoutUser({ commit }) {
    // Clear localStorage
    localStorage.removeItem('auth_token')
    localStorage.removeItem('user_data')
    
    // Clear state
    commit('CLEAR_AUTH')
  },
  
  // Update user profile
  async updateUserProfile({ commit, state }, profileData) {
    try {
      commit('SET_LOADING', true)
      
      // This would be an API call in a real app
      const updatedUser = { ...state.user, ...profileData }
      
      // Update localStorage
      localStorage.setItem('user_data', JSON.stringify(updatedUser))
      
      // Update state
      commit('SET_USER', updatedUser)
      
      return { success: true, user: updatedUser }
      
    } catch (error) {
      console.error('Profile update error:', error)
      throw error
    } finally {
      commit('SET_LOADING', false)
    }
  }
}

export default {
  namespaced: true,
  state,
  getters,
  mutations,
  actions
}
