// User Model
import db from "../config/database.js";

// Get all users (admin only)
export const getAllUsers = (result) => {
    db.query("SELECT user_id, user_name, user_email, user_phone, user_birth, user_gender FROM user", (err, results) => {
        if (err) {
            console.error("Error fetching users:", err);
            result(err, null);
        } else {
            result(null, results);
        }
    });
};

// Get single user by email
export const getUserByEmail = (email, result) => {
    db.query("SELECT user_id, user_name, user_email, user_password FROM user WHERE user_email = ?", [email], (err, results) => {
        if (err) {
            console.error("Error fetching user by email:", err);
            result(err, null);
        } else {
            result(null, results[0]);
        }
    });
};

// Get user by ID
export const getUserById = (id, result) => {
    db.query("SELECT user_id, user_name, user_email, user_phone, user_birth, user_gender FROM user WHERE user_id = ?", [id], (err, results) => {
        if (err) {
            console.error("Error fetching user by ID:", err);
            result(err, null);
        } else {
            result(null, results[0]);
        }
    });
};

// Insert new user
export const insertUser = (data, result) => {
    db.query("INSERT INTO user SET ?", data, (err, results) => {
        if (err) {
            console.error("Error inserting user:", err);
            result(err, null);
        } else {
            result(null, results);
        }
    });
};

// Update user
export const updateUser = (id, data, result) => {
    db.query("UPDATE user SET ? WHERE user_id = ?", [data, id], (err, results) => {
        if (err) {
            console.error("Error updating user:", err);
            result(err, null);
        } else {
            result(null, results);
        }
    });
};

// Delete user
export const deleteUser = (id, result) => {
    db.query("DELETE FROM user WHERE user_id = ?", [id], (err, results) => {
        if (err) {
            console.error("Error deleting user:", err);
            result(err, null);
        } else {
            result(null, results);
        }
    });
};

// Check if email exists
export const checkEmailExists = (email, result) => {
    db.query("SELECT COUNT(*) as count FROM user WHERE user_email = ?", [email], (err, results) => {
        if (err) {
            console.error("Error checking email:", err);
            result(err, null);
        } else {
            result(null, results[0].count > 0);
        }
    });
};
